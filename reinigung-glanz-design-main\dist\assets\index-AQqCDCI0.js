var mc=e=>{throw TypeError(e)};var ll=(e,t,n)=>t.has(e)||mc("Cannot "+n);var b=(e,t,n)=>(ll(e,t,"read from private field"),n?n.call(e):t.get(e)),Z=(e,t,n)=>t.has(e)?mc("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),B=(e,t,n,r)=>(ll(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n),Pe=(e,t,n)=>(ll(e,t,"access private method"),n);var fi=(e,t,n,r)=>({set _(o){B(e,t,o,n)},get _(){return b(e,t,r)}});function vg(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const i=Object.getOwnPropertyDescriptor(r,o);i&&Object.defineProperty(e,o,i.get?i:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();function mf(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var gf={exports:{}},Ts={},vf={exports:{}},Y={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ti=Symbol.for("react.element"),yg=Symbol.for("react.portal"),wg=Symbol.for("react.fragment"),xg=Symbol.for("react.strict_mode"),Sg=Symbol.for("react.profiler"),Eg=Symbol.for("react.provider"),Cg=Symbol.for("react.context"),kg=Symbol.for("react.forward_ref"),Pg=Symbol.for("react.suspense"),bg=Symbol.for("react.memo"),Tg=Symbol.for("react.lazy"),gc=Symbol.iterator;function Ng(e){return e===null||typeof e!="object"?null:(e=gc&&e[gc]||e["@@iterator"],typeof e=="function"?e:null)}var yf={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},wf=Object.assign,xf={};function Jr(e,t,n){this.props=e,this.context=t,this.refs=xf,this.updater=n||yf}Jr.prototype.isReactComponent={};Jr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Jr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Sf(){}Sf.prototype=Jr.prototype;function qa(e,t,n){this.props=e,this.context=t,this.refs=xf,this.updater=n||yf}var Za=qa.prototype=new Sf;Za.constructor=qa;wf(Za,Jr.prototype);Za.isPureReactComponent=!0;var vc=Array.isArray,Ef=Object.prototype.hasOwnProperty,Ja={current:null},Cf={key:!0,ref:!0,__self:!0,__source:!0};function kf(e,t,n){var r,o={},i=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(i=""+t.key),t)Ef.call(t,r)&&!Cf.hasOwnProperty(r)&&(o[r]=t[r]);var l=arguments.length-2;if(l===1)o.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];o.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)o[r]===void 0&&(o[r]=l[r]);return{$$typeof:ti,type:e,key:i,ref:s,props:o,_owner:Ja.current}}function Rg(e,t){return{$$typeof:ti,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function eu(e){return typeof e=="object"&&e!==null&&e.$$typeof===ti}function Og(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var yc=/\/+/g;function al(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Og(""+e.key):t.toString(36)}function Mi(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case ti:case yg:s=!0}}if(s)return s=e,o=o(s),e=r===""?"."+al(s,0):r,vc(o)?(n="",e!=null&&(n=e.replace(yc,"$&/")+"/"),Mi(o,t,n,"",function(u){return u})):o!=null&&(eu(o)&&(o=Rg(o,n+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(yc,"$&/")+"/")+e)),t.push(o)),1;if(s=0,r=r===""?".":r+":",vc(e))for(var l=0;l<e.length;l++){i=e[l];var a=r+al(i,l);s+=Mi(i,t,n,a,o)}else if(a=Ng(e),typeof a=="function")for(e=a.call(e),l=0;!(i=e.next()).done;)i=i.value,a=r+al(i,l++),s+=Mi(i,t,n,a,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function pi(e,t,n){if(e==null)return e;var r=[],o=0;return Mi(e,r,"","",function(i){return t.call(n,i,o++)}),r}function _g(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Me={current:null},Di={transition:null},Ag={ReactCurrentDispatcher:Me,ReactCurrentBatchConfig:Di,ReactCurrentOwner:Ja};function Pf(){throw Error("act(...) is not supported in production builds of React.")}Y.Children={map:pi,forEach:function(e,t,n){pi(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return pi(e,function(){t++}),t},toArray:function(e){return pi(e,function(t){return t})||[]},only:function(e){if(!eu(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};Y.Component=Jr;Y.Fragment=wg;Y.Profiler=Sg;Y.PureComponent=qa;Y.StrictMode=xg;Y.Suspense=Pg;Y.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Ag;Y.act=Pf;Y.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=wf({},e.props),o=e.key,i=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,s=Ja.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)Ef.call(t,a)&&!Cf.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var u=0;u<a;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:ti,type:e.type,key:o,ref:i,props:r,_owner:s}};Y.createContext=function(e){return e={$$typeof:Cg,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Eg,_context:e},e.Consumer=e};Y.createElement=kf;Y.createFactory=function(e){var t=kf.bind(null,e);return t.type=e,t};Y.createRef=function(){return{current:null}};Y.forwardRef=function(e){return{$$typeof:kg,render:e}};Y.isValidElement=eu;Y.lazy=function(e){return{$$typeof:Tg,_payload:{_status:-1,_result:e},_init:_g}};Y.memo=function(e,t){return{$$typeof:bg,type:e,compare:t===void 0?null:t}};Y.startTransition=function(e){var t=Di.transition;Di.transition={};try{e()}finally{Di.transition=t}};Y.unstable_act=Pf;Y.useCallback=function(e,t){return Me.current.useCallback(e,t)};Y.useContext=function(e){return Me.current.useContext(e)};Y.useDebugValue=function(){};Y.useDeferredValue=function(e){return Me.current.useDeferredValue(e)};Y.useEffect=function(e,t){return Me.current.useEffect(e,t)};Y.useId=function(){return Me.current.useId()};Y.useImperativeHandle=function(e,t,n){return Me.current.useImperativeHandle(e,t,n)};Y.useInsertionEffect=function(e,t){return Me.current.useInsertionEffect(e,t)};Y.useLayoutEffect=function(e,t){return Me.current.useLayoutEffect(e,t)};Y.useMemo=function(e,t){return Me.current.useMemo(e,t)};Y.useReducer=function(e,t,n){return Me.current.useReducer(e,t,n)};Y.useRef=function(e){return Me.current.useRef(e)};Y.useState=function(e){return Me.current.useState(e)};Y.useSyncExternalStore=function(e,t,n){return Me.current.useSyncExternalStore(e,t,n)};Y.useTransition=function(){return Me.current.useTransition()};Y.version="18.3.1";vf.exports=Y;var x=vf.exports;const _=mf(x),bf=vg({__proto__:null,default:_},[x]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Lg=x,jg=Symbol.for("react.element"),Mg=Symbol.for("react.fragment"),Dg=Object.prototype.hasOwnProperty,Ig=Lg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,zg={key:!0,ref:!0,__self:!0,__source:!0};function Tf(e,t,n){var r,o={},i=null,s=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)Dg.call(t,r)&&!zg.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:jg,type:e,key:i,ref:s,props:o,_owner:Ig.current}}Ts.Fragment=Mg;Ts.jsx=Tf;Ts.jsxs=Tf;gf.exports=Ts;var E=gf.exports,Nf={exports:{}},qe={},Rf={exports:{}},Of={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(T,N){var M=T.length;T.push(N);e:for(;0<M;){var W=M-1>>>1,z=T[W];if(0<o(z,N))T[W]=N,T[M]=z,M=W;else break e}}function n(T){return T.length===0?null:T[0]}function r(T){if(T.length===0)return null;var N=T[0],M=T.pop();if(M!==N){T[0]=M;e:for(var W=0,z=T.length,K=z>>>1;W<K;){var X=2*(W+1)-1,he=T[X],ke=X+1,J=T[ke];if(0>o(he,M))ke<z&&0>o(J,he)?(T[W]=J,T[ke]=M,W=ke):(T[W]=he,T[X]=M,W=X);else if(ke<z&&0>o(J,M))T[W]=J,T[ke]=M,W=ke;else break e}}return N}function o(T,N){var M=T.sortIndex-N.sortIndex;return M!==0?M:T.id-N.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var s=Date,l=s.now();e.unstable_now=function(){return s.now()-l}}var a=[],u=[],c=1,f=null,h=3,d=!1,w=!1,v=!1,y=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(T){for(var N=n(u);N!==null;){if(N.callback===null)r(u);else if(N.startTime<=T)r(u),N.sortIndex=N.expirationTime,t(a,N);else break;N=n(u)}}function S(T){if(v=!1,g(T),!w)if(n(a)!==null)w=!0,$(C);else{var N=n(u);N!==null&&V(S,N.startTime-T)}}function C(T,N){w=!1,v&&(v=!1,m(R),R=-1),d=!0;var M=h;try{for(g(N),f=n(a);f!==null&&(!(f.expirationTime>N)||T&&!F());){var W=f.callback;if(typeof W=="function"){f.callback=null,h=f.priorityLevel;var z=W(f.expirationTime<=N);N=e.unstable_now(),typeof z=="function"?f.callback=z:f===n(a)&&r(a),g(N)}else r(a);f=n(a)}if(f!==null)var K=!0;else{var X=n(u);X!==null&&V(S,X.startTime-N),K=!1}return K}finally{f=null,h=M,d=!1}}var k=!1,P=null,R=-1,L=5,A=-1;function F(){return!(e.unstable_now()-A<L)}function I(){if(P!==null){var T=e.unstable_now();A=T;var N=!0;try{N=P(!0,T)}finally{N?Q():(k=!1,P=null)}}else k=!1}var Q;if(typeof p=="function")Q=function(){p(I)};else if(typeof MessageChannel<"u"){var j=new MessageChannel,G=j.port2;j.port1.onmessage=I,Q=function(){G.postMessage(null)}}else Q=function(){y(I,0)};function $(T){P=T,k||(k=!0,Q())}function V(T,N){R=y(function(){T(e.unstable_now())},N)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(T){T.callback=null},e.unstable_continueExecution=function(){w||d||(w=!0,$(C))},e.unstable_forceFrameRate=function(T){0>T||125<T?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):L=0<T?Math.floor(1e3/T):5},e.unstable_getCurrentPriorityLevel=function(){return h},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(T){switch(h){case 1:case 2:case 3:var N=3;break;default:N=h}var M=h;h=N;try{return T()}finally{h=M}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(T,N){switch(T){case 1:case 2:case 3:case 4:case 5:break;default:T=3}var M=h;h=T;try{return N()}finally{h=M}},e.unstable_scheduleCallback=function(T,N,M){var W=e.unstable_now();switch(typeof M=="object"&&M!==null?(M=M.delay,M=typeof M=="number"&&0<M?W+M:W):M=W,T){case 1:var z=-1;break;case 2:z=250;break;case 5:z=**********;break;case 4:z=1e4;break;default:z=5e3}return z=M+z,T={id:c++,callback:N,priorityLevel:T,startTime:M,expirationTime:z,sortIndex:-1},M>W?(T.sortIndex=M,t(u,T),n(a)===null&&T===n(u)&&(v?(m(R),R=-1):v=!0,V(S,M-W))):(T.sortIndex=z,t(a,T),w||d||(w=!0,$(C))),T},e.unstable_shouldYield=F,e.unstable_wrapCallback=function(T){var N=h;return function(){var M=h;h=N;try{return T.apply(this,arguments)}finally{h=M}}}})(Of);Rf.exports=Of;var Fg=Rf.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var $g=x,Xe=Fg;function O(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var _f=new Set,Ao={};function ir(e,t){Wr(e,t),Wr(e+"Capture",t)}function Wr(e,t){for(Ao[e]=t,e=0;e<t.length;e++)_f.add(t[e])}var Wt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ul=Object.prototype.hasOwnProperty,Ug=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,wc={},xc={};function Bg(e){return Ul.call(xc,e)?!0:Ul.call(wc,e)?!1:Ug.test(e)?xc[e]=!0:(wc[e]=!0,!1)}function Vg(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Wg(e,t,n,r){if(t===null||typeof t>"u"||Vg(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function De(e,t,n,r,o,i,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=s}var Ce={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Ce[e]=new De(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Ce[t]=new De(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Ce[e]=new De(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Ce[e]=new De(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Ce[e]=new De(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Ce[e]=new De(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Ce[e]=new De(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Ce[e]=new De(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Ce[e]=new De(e,5,!1,e.toLowerCase(),null,!1,!1)});var tu=/[\-:]([a-z])/g;function nu(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(tu,nu);Ce[t]=new De(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(tu,nu);Ce[t]=new De(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(tu,nu);Ce[t]=new De(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Ce[e]=new De(e,1,!1,e.toLowerCase(),null,!1,!1)});Ce.xlinkHref=new De("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Ce[e]=new De(e,1,!1,e.toLowerCase(),null,!0,!0)});function ru(e,t,n,r){var o=Ce.hasOwnProperty(t)?Ce[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Wg(t,n,o,r)&&(n=null),r||o===null?Bg(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Xt=$g.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,hi=Symbol.for("react.element"),mr=Symbol.for("react.portal"),gr=Symbol.for("react.fragment"),ou=Symbol.for("react.strict_mode"),Bl=Symbol.for("react.profiler"),Af=Symbol.for("react.provider"),Lf=Symbol.for("react.context"),iu=Symbol.for("react.forward_ref"),Vl=Symbol.for("react.suspense"),Wl=Symbol.for("react.suspense_list"),su=Symbol.for("react.memo"),an=Symbol.for("react.lazy"),jf=Symbol.for("react.offscreen"),Sc=Symbol.iterator;function lo(e){return e===null||typeof e!="object"?null:(e=Sc&&e[Sc]||e["@@iterator"],typeof e=="function"?e:null)}var ce=Object.assign,ul;function yo(e){if(ul===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);ul=t&&t[1]||""}return`
`+ul+e}var cl=!1;function dl(e,t){if(!e||cl)return"";cl=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),i=r.stack.split(`
`),s=o.length-1,l=i.length-1;1<=s&&0<=l&&o[s]!==i[l];)l--;for(;1<=s&&0<=l;s--,l--)if(o[s]!==i[l]){if(s!==1||l!==1)do if(s--,l--,0>l||o[s]!==i[l]){var a=`
`+o[s].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=s&&0<=l);break}}}finally{cl=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?yo(e):""}function Hg(e){switch(e.tag){case 5:return yo(e.type);case 16:return yo("Lazy");case 13:return yo("Suspense");case 19:return yo("SuspenseList");case 0:case 2:case 15:return e=dl(e.type,!1),e;case 11:return e=dl(e.type.render,!1),e;case 1:return e=dl(e.type,!0),e;default:return""}}function Hl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case gr:return"Fragment";case mr:return"Portal";case Bl:return"Profiler";case ou:return"StrictMode";case Vl:return"Suspense";case Wl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Lf:return(e.displayName||"Context")+".Consumer";case Af:return(e._context.displayName||"Context")+".Provider";case iu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case su:return t=e.displayName||null,t!==null?t:Hl(e.type)||"Memo";case an:t=e._payload,e=e._init;try{return Hl(e(t))}catch{}}return null}function Qg(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Hl(t);case 8:return t===ou?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Nn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Mf(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Kg(e){var t=Mf(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(s){r=""+s,i.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function mi(e){e._valueTracker||(e._valueTracker=Kg(e))}function Df(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Mf(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Xi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Ql(e,t){var n=t.checked;return ce({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Ec(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Nn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function If(e,t){t=t.checked,t!=null&&ru(e,"checked",t,!1)}function Kl(e,t){If(e,t);var n=Nn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Yl(e,t.type,n):t.hasOwnProperty("defaultValue")&&Yl(e,t.type,Nn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Cc(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Yl(e,t,n){(t!=="number"||Xi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var wo=Array.isArray;function Tr(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Nn(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Gl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(O(91));return ce({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function kc(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(O(92));if(wo(n)){if(1<n.length)throw Error(O(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Nn(n)}}function zf(e,t){var n=Nn(t.value),r=Nn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Pc(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Ff(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Xl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Ff(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var gi,$f=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(gi=gi||document.createElement("div"),gi.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=gi.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Lo(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Eo={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Yg=["Webkit","ms","Moz","O"];Object.keys(Eo).forEach(function(e){Yg.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Eo[t]=Eo[e]})});function Uf(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Eo.hasOwnProperty(e)&&Eo[e]?(""+t).trim():t+"px"}function Bf(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=Uf(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var Gg=ce({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ql(e,t){if(t){if(Gg[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(O(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(O(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(O(61))}if(t.style!=null&&typeof t.style!="object")throw Error(O(62))}}function Zl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Jl=null;function lu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ea=null,Nr=null,Rr=null;function bc(e){if(e=oi(e)){if(typeof ea!="function")throw Error(O(280));var t=e.stateNode;t&&(t=As(t),ea(e.stateNode,e.type,t))}}function Vf(e){Nr?Rr?Rr.push(e):Rr=[e]:Nr=e}function Wf(){if(Nr){var e=Nr,t=Rr;if(Rr=Nr=null,bc(e),t)for(e=0;e<t.length;e++)bc(t[e])}}function Hf(e,t){return e(t)}function Qf(){}var fl=!1;function Kf(e,t,n){if(fl)return e(t,n);fl=!0;try{return Hf(e,t,n)}finally{fl=!1,(Nr!==null||Rr!==null)&&(Qf(),Wf())}}function jo(e,t){var n=e.stateNode;if(n===null)return null;var r=As(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(O(231,t,typeof n));return n}var ta=!1;if(Wt)try{var ao={};Object.defineProperty(ao,"passive",{get:function(){ta=!0}}),window.addEventListener("test",ao,ao),window.removeEventListener("test",ao,ao)}catch{ta=!1}function Xg(e,t,n,r,o,i,s,l,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Co=!1,qi=null,Zi=!1,na=null,qg={onError:function(e){Co=!0,qi=e}};function Zg(e,t,n,r,o,i,s,l,a){Co=!1,qi=null,Xg.apply(qg,arguments)}function Jg(e,t,n,r,o,i,s,l,a){if(Zg.apply(this,arguments),Co){if(Co){var u=qi;Co=!1,qi=null}else throw Error(O(198));Zi||(Zi=!0,na=u)}}function sr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Yf(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Tc(e){if(sr(e)!==e)throw Error(O(188))}function ev(e){var t=e.alternate;if(!t){if(t=sr(e),t===null)throw Error(O(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return Tc(o),e;if(i===r)return Tc(o),t;i=i.sibling}throw Error(O(188))}if(n.return!==r.return)n=o,r=i;else{for(var s=!1,l=o.child;l;){if(l===n){s=!0,n=o,r=i;break}if(l===r){s=!0,r=o,n=i;break}l=l.sibling}if(!s){for(l=i.child;l;){if(l===n){s=!0,n=i,r=o;break}if(l===r){s=!0,r=i,n=o;break}l=l.sibling}if(!s)throw Error(O(189))}}if(n.alternate!==r)throw Error(O(190))}if(n.tag!==3)throw Error(O(188));return n.stateNode.current===n?e:t}function Gf(e){return e=ev(e),e!==null?Xf(e):null}function Xf(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Xf(e);if(t!==null)return t;e=e.sibling}return null}var qf=Xe.unstable_scheduleCallback,Nc=Xe.unstable_cancelCallback,tv=Xe.unstable_shouldYield,nv=Xe.unstable_requestPaint,pe=Xe.unstable_now,rv=Xe.unstable_getCurrentPriorityLevel,au=Xe.unstable_ImmediatePriority,Zf=Xe.unstable_UserBlockingPriority,Ji=Xe.unstable_NormalPriority,ov=Xe.unstable_LowPriority,Jf=Xe.unstable_IdlePriority,Ns=null,_t=null;function iv(e){if(_t&&typeof _t.onCommitFiberRoot=="function")try{_t.onCommitFiberRoot(Ns,e,void 0,(e.current.flags&128)===128)}catch{}}var yt=Math.clz32?Math.clz32:av,sv=Math.log,lv=Math.LN2;function av(e){return e>>>=0,e===0?32:31-(sv(e)/lv|0)|0}var vi=64,yi=4194304;function xo(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function es(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,s=n&268435455;if(s!==0){var l=s&~o;l!==0?r=xo(l):(i&=s,i!==0&&(r=xo(i)))}else s=n&~o,s!==0?r=xo(s):i!==0&&(r=xo(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-yt(t),o=1<<n,r|=e[n],t&=~o;return r}function uv(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function cv(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var s=31-yt(i),l=1<<s,a=o[s];a===-1?(!(l&n)||l&r)&&(o[s]=uv(l,t)):a<=t&&(e.expiredLanes|=l),i&=~l}}function ra(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function ep(){var e=vi;return vi<<=1,!(vi&4194240)&&(vi=64),e}function pl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function ni(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-yt(t),e[t]=n}function dv(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-yt(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function uu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-yt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var ee=0;function tp(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var np,cu,rp,op,ip,oa=!1,wi=[],xn=null,Sn=null,En=null,Mo=new Map,Do=new Map,cn=[],fv="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Rc(e,t){switch(e){case"focusin":case"focusout":xn=null;break;case"dragenter":case"dragleave":Sn=null;break;case"mouseover":case"mouseout":En=null;break;case"pointerover":case"pointerout":Mo.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Do.delete(t.pointerId)}}function uo(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=oi(t),t!==null&&cu(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function pv(e,t,n,r,o){switch(t){case"focusin":return xn=uo(xn,e,t,n,r,o),!0;case"dragenter":return Sn=uo(Sn,e,t,n,r,o),!0;case"mouseover":return En=uo(En,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return Mo.set(i,uo(Mo.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,Do.set(i,uo(Do.get(i)||null,e,t,n,r,o)),!0}return!1}function sp(e){var t=Bn(e.target);if(t!==null){var n=sr(t);if(n!==null){if(t=n.tag,t===13){if(t=Yf(n),t!==null){e.blockedOn=t,ip(e.priority,function(){rp(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ii(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=ia(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Jl=r,n.target.dispatchEvent(r),Jl=null}else return t=oi(n),t!==null&&cu(t),e.blockedOn=n,!1;t.shift()}return!0}function Oc(e,t,n){Ii(e)&&n.delete(t)}function hv(){oa=!1,xn!==null&&Ii(xn)&&(xn=null),Sn!==null&&Ii(Sn)&&(Sn=null),En!==null&&Ii(En)&&(En=null),Mo.forEach(Oc),Do.forEach(Oc)}function co(e,t){e.blockedOn===t&&(e.blockedOn=null,oa||(oa=!0,Xe.unstable_scheduleCallback(Xe.unstable_NormalPriority,hv)))}function Io(e){function t(o){return co(o,e)}if(0<wi.length){co(wi[0],e);for(var n=1;n<wi.length;n++){var r=wi[n];r.blockedOn===e&&(r.blockedOn=null)}}for(xn!==null&&co(xn,e),Sn!==null&&co(Sn,e),En!==null&&co(En,e),Mo.forEach(t),Do.forEach(t),n=0;n<cn.length;n++)r=cn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<cn.length&&(n=cn[0],n.blockedOn===null);)sp(n),n.blockedOn===null&&cn.shift()}var Or=Xt.ReactCurrentBatchConfig,ts=!0;function mv(e,t,n,r){var o=ee,i=Or.transition;Or.transition=null;try{ee=1,du(e,t,n,r)}finally{ee=o,Or.transition=i}}function gv(e,t,n,r){var o=ee,i=Or.transition;Or.transition=null;try{ee=4,du(e,t,n,r)}finally{ee=o,Or.transition=i}}function du(e,t,n,r){if(ts){var o=ia(e,t,n,r);if(o===null)Cl(e,t,r,ns,n),Rc(e,r);else if(pv(o,e,t,n,r))r.stopPropagation();else if(Rc(e,r),t&4&&-1<fv.indexOf(e)){for(;o!==null;){var i=oi(o);if(i!==null&&np(i),i=ia(e,t,n,r),i===null&&Cl(e,t,r,ns,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else Cl(e,t,r,null,n)}}var ns=null;function ia(e,t,n,r){if(ns=null,e=lu(r),e=Bn(e),e!==null)if(t=sr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Yf(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ns=e,null}function lp(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(rv()){case au:return 1;case Zf:return 4;case Ji:case ov:return 16;case Jf:return 536870912;default:return 16}default:return 16}}var vn=null,fu=null,zi=null;function ap(){if(zi)return zi;var e,t=fu,n=t.length,r,o="value"in vn?vn.value:vn.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===o[i-r];r++);return zi=o.slice(e,1<r?1-r:void 0)}function Fi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function xi(){return!0}function _c(){return!1}function Ze(e){function t(n,r,o,i,s){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(i):i[l]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?xi:_c,this.isPropagationStopped=_c,this}return ce(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=xi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=xi)},persist:function(){},isPersistent:xi}),t}var eo={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},pu=Ze(eo),ri=ce({},eo,{view:0,detail:0}),vv=Ze(ri),hl,ml,fo,Rs=ce({},ri,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:hu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==fo&&(fo&&e.type==="mousemove"?(hl=e.screenX-fo.screenX,ml=e.screenY-fo.screenY):ml=hl=0,fo=e),hl)},movementY:function(e){return"movementY"in e?e.movementY:ml}}),Ac=Ze(Rs),yv=ce({},Rs,{dataTransfer:0}),wv=Ze(yv),xv=ce({},ri,{relatedTarget:0}),gl=Ze(xv),Sv=ce({},eo,{animationName:0,elapsedTime:0,pseudoElement:0}),Ev=Ze(Sv),Cv=ce({},eo,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),kv=Ze(Cv),Pv=ce({},eo,{data:0}),Lc=Ze(Pv),bv={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Tv={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Nv={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Rv(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Nv[e])?!!t[e]:!1}function hu(){return Rv}var Ov=ce({},ri,{key:function(e){if(e.key){var t=bv[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Fi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Tv[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:hu,charCode:function(e){return e.type==="keypress"?Fi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Fi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),_v=Ze(Ov),Av=ce({},Rs,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),jc=Ze(Av),Lv=ce({},ri,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:hu}),jv=Ze(Lv),Mv=ce({},eo,{propertyName:0,elapsedTime:0,pseudoElement:0}),Dv=Ze(Mv),Iv=ce({},Rs,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),zv=Ze(Iv),Fv=[9,13,27,32],mu=Wt&&"CompositionEvent"in window,ko=null;Wt&&"documentMode"in document&&(ko=document.documentMode);var $v=Wt&&"TextEvent"in window&&!ko,up=Wt&&(!mu||ko&&8<ko&&11>=ko),Mc=" ",Dc=!1;function cp(e,t){switch(e){case"keyup":return Fv.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function dp(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var vr=!1;function Uv(e,t){switch(e){case"compositionend":return dp(t);case"keypress":return t.which!==32?null:(Dc=!0,Mc);case"textInput":return e=t.data,e===Mc&&Dc?null:e;default:return null}}function Bv(e,t){if(vr)return e==="compositionend"||!mu&&cp(e,t)?(e=ap(),zi=fu=vn=null,vr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return up&&t.locale!=="ko"?null:t.data;default:return null}}var Vv={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ic(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Vv[e.type]:t==="textarea"}function fp(e,t,n,r){Vf(r),t=rs(t,"onChange"),0<t.length&&(n=new pu("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Po=null,zo=null;function Wv(e){Cp(e,0)}function Os(e){var t=xr(e);if(Df(t))return e}function Hv(e,t){if(e==="change")return t}var pp=!1;if(Wt){var vl;if(Wt){var yl="oninput"in document;if(!yl){var zc=document.createElement("div");zc.setAttribute("oninput","return;"),yl=typeof zc.oninput=="function"}vl=yl}else vl=!1;pp=vl&&(!document.documentMode||9<document.documentMode)}function Fc(){Po&&(Po.detachEvent("onpropertychange",hp),zo=Po=null)}function hp(e){if(e.propertyName==="value"&&Os(zo)){var t=[];fp(t,zo,e,lu(e)),Kf(Wv,t)}}function Qv(e,t,n){e==="focusin"?(Fc(),Po=t,zo=n,Po.attachEvent("onpropertychange",hp)):e==="focusout"&&Fc()}function Kv(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Os(zo)}function Yv(e,t){if(e==="click")return Os(t)}function Gv(e,t){if(e==="input"||e==="change")return Os(t)}function Xv(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var xt=typeof Object.is=="function"?Object.is:Xv;function Fo(e,t){if(xt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!Ul.call(t,o)||!xt(e[o],t[o]))return!1}return!0}function $c(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Uc(e,t){var n=$c(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=$c(n)}}function mp(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?mp(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function gp(){for(var e=window,t=Xi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Xi(e.document)}return t}function gu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function qv(e){var t=gp(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&mp(n.ownerDocument.documentElement,n)){if(r!==null&&gu(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=Uc(n,i);var s=Uc(n,r);o&&s&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Zv=Wt&&"documentMode"in document&&11>=document.documentMode,yr=null,sa=null,bo=null,la=!1;function Bc(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;la||yr==null||yr!==Xi(r)||(r=yr,"selectionStart"in r&&gu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),bo&&Fo(bo,r)||(bo=r,r=rs(sa,"onSelect"),0<r.length&&(t=new pu("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=yr)))}function Si(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var wr={animationend:Si("Animation","AnimationEnd"),animationiteration:Si("Animation","AnimationIteration"),animationstart:Si("Animation","AnimationStart"),transitionend:Si("Transition","TransitionEnd")},wl={},vp={};Wt&&(vp=document.createElement("div").style,"AnimationEvent"in window||(delete wr.animationend.animation,delete wr.animationiteration.animation,delete wr.animationstart.animation),"TransitionEvent"in window||delete wr.transitionend.transition);function _s(e){if(wl[e])return wl[e];if(!wr[e])return e;var t=wr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in vp)return wl[e]=t[n];return e}var yp=_s("animationend"),wp=_s("animationiteration"),xp=_s("animationstart"),Sp=_s("transitionend"),Ep=new Map,Vc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function jn(e,t){Ep.set(e,t),ir(t,[e])}for(var xl=0;xl<Vc.length;xl++){var Sl=Vc[xl],Jv=Sl.toLowerCase(),ey=Sl[0].toUpperCase()+Sl.slice(1);jn(Jv,"on"+ey)}jn(yp,"onAnimationEnd");jn(wp,"onAnimationIteration");jn(xp,"onAnimationStart");jn("dblclick","onDoubleClick");jn("focusin","onFocus");jn("focusout","onBlur");jn(Sp,"onTransitionEnd");Wr("onMouseEnter",["mouseout","mouseover"]);Wr("onMouseLeave",["mouseout","mouseover"]);Wr("onPointerEnter",["pointerout","pointerover"]);Wr("onPointerLeave",["pointerout","pointerover"]);ir("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));ir("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));ir("onBeforeInput",["compositionend","keypress","textInput","paste"]);ir("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));ir("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));ir("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var So="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),ty=new Set("cancel close invalid load scroll toggle".split(" ").concat(So));function Wc(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Jg(r,t,void 0,e),e.currentTarget=null}function Cp(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var s=r.length-1;0<=s;s--){var l=r[s],a=l.instance,u=l.currentTarget;if(l=l.listener,a!==i&&o.isPropagationStopped())break e;Wc(o,l,u),i=a}else for(s=0;s<r.length;s++){if(l=r[s],a=l.instance,u=l.currentTarget,l=l.listener,a!==i&&o.isPropagationStopped())break e;Wc(o,l,u),i=a}}}if(Zi)throw e=na,Zi=!1,na=null,e}function oe(e,t){var n=t[fa];n===void 0&&(n=t[fa]=new Set);var r=e+"__bubble";n.has(r)||(kp(t,e,2,!1),n.add(r))}function El(e,t,n){var r=0;t&&(r|=4),kp(n,e,r,t)}var Ei="_reactListening"+Math.random().toString(36).slice(2);function $o(e){if(!e[Ei]){e[Ei]=!0,_f.forEach(function(n){n!=="selectionchange"&&(ty.has(n)||El(n,!1,e),El(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ei]||(t[Ei]=!0,El("selectionchange",!1,t))}}function kp(e,t,n,r){switch(lp(t)){case 1:var o=mv;break;case 4:o=gv;break;default:o=du}n=o.bind(null,t,n,e),o=void 0,!ta||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Cl(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var l=r.stateNode.containerInfo;if(l===o||l.nodeType===8&&l.parentNode===o)break;if(s===4)for(s=r.return;s!==null;){var a=s.tag;if((a===3||a===4)&&(a=s.stateNode.containerInfo,a===o||a.nodeType===8&&a.parentNode===o))return;s=s.return}for(;l!==null;){if(s=Bn(l),s===null)return;if(a=s.tag,a===5||a===6){r=i=s;continue e}l=l.parentNode}}r=r.return}Kf(function(){var u=i,c=lu(n),f=[];e:{var h=Ep.get(e);if(h!==void 0){var d=pu,w=e;switch(e){case"keypress":if(Fi(n)===0)break e;case"keydown":case"keyup":d=_v;break;case"focusin":w="focus",d=gl;break;case"focusout":w="blur",d=gl;break;case"beforeblur":case"afterblur":d=gl;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":d=Ac;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":d=wv;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":d=jv;break;case yp:case wp:case xp:d=Ev;break;case Sp:d=Dv;break;case"scroll":d=vv;break;case"wheel":d=zv;break;case"copy":case"cut":case"paste":d=kv;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":d=jc}var v=(t&4)!==0,y=!v&&e==="scroll",m=v?h!==null?h+"Capture":null:h;v=[];for(var p=u,g;p!==null;){g=p;var S=g.stateNode;if(g.tag===5&&S!==null&&(g=S,m!==null&&(S=jo(p,m),S!=null&&v.push(Uo(p,S,g)))),y)break;p=p.return}0<v.length&&(h=new d(h,w,null,n,c),f.push({event:h,listeners:v}))}}if(!(t&7)){e:{if(h=e==="mouseover"||e==="pointerover",d=e==="mouseout"||e==="pointerout",h&&n!==Jl&&(w=n.relatedTarget||n.fromElement)&&(Bn(w)||w[Ht]))break e;if((d||h)&&(h=c.window===c?c:(h=c.ownerDocument)?h.defaultView||h.parentWindow:window,d?(w=n.relatedTarget||n.toElement,d=u,w=w?Bn(w):null,w!==null&&(y=sr(w),w!==y||w.tag!==5&&w.tag!==6)&&(w=null)):(d=null,w=u),d!==w)){if(v=Ac,S="onMouseLeave",m="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(v=jc,S="onPointerLeave",m="onPointerEnter",p="pointer"),y=d==null?h:xr(d),g=w==null?h:xr(w),h=new v(S,p+"leave",d,n,c),h.target=y,h.relatedTarget=g,S=null,Bn(c)===u&&(v=new v(m,p+"enter",w,n,c),v.target=g,v.relatedTarget=y,S=v),y=S,d&&w)t:{for(v=d,m=w,p=0,g=v;g;g=hr(g))p++;for(g=0,S=m;S;S=hr(S))g++;for(;0<p-g;)v=hr(v),p--;for(;0<g-p;)m=hr(m),g--;for(;p--;){if(v===m||m!==null&&v===m.alternate)break t;v=hr(v),m=hr(m)}v=null}else v=null;d!==null&&Hc(f,h,d,v,!1),w!==null&&y!==null&&Hc(f,y,w,v,!0)}}e:{if(h=u?xr(u):window,d=h.nodeName&&h.nodeName.toLowerCase(),d==="select"||d==="input"&&h.type==="file")var C=Hv;else if(Ic(h))if(pp)C=Gv;else{C=Kv;var k=Qv}else(d=h.nodeName)&&d.toLowerCase()==="input"&&(h.type==="checkbox"||h.type==="radio")&&(C=Yv);if(C&&(C=C(e,u))){fp(f,C,n,c);break e}k&&k(e,h,u),e==="focusout"&&(k=h._wrapperState)&&k.controlled&&h.type==="number"&&Yl(h,"number",h.value)}switch(k=u?xr(u):window,e){case"focusin":(Ic(k)||k.contentEditable==="true")&&(yr=k,sa=u,bo=null);break;case"focusout":bo=sa=yr=null;break;case"mousedown":la=!0;break;case"contextmenu":case"mouseup":case"dragend":la=!1,Bc(f,n,c);break;case"selectionchange":if(Zv)break;case"keydown":case"keyup":Bc(f,n,c)}var P;if(mu)e:{switch(e){case"compositionstart":var R="onCompositionStart";break e;case"compositionend":R="onCompositionEnd";break e;case"compositionupdate":R="onCompositionUpdate";break e}R=void 0}else vr?cp(e,n)&&(R="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(R="onCompositionStart");R&&(up&&n.locale!=="ko"&&(vr||R!=="onCompositionStart"?R==="onCompositionEnd"&&vr&&(P=ap()):(vn=c,fu="value"in vn?vn.value:vn.textContent,vr=!0)),k=rs(u,R),0<k.length&&(R=new Lc(R,e,null,n,c),f.push({event:R,listeners:k}),P?R.data=P:(P=dp(n),P!==null&&(R.data=P)))),(P=$v?Uv(e,n):Bv(e,n))&&(u=rs(u,"onBeforeInput"),0<u.length&&(c=new Lc("onBeforeInput","beforeinput",null,n,c),f.push({event:c,listeners:u}),c.data=P))}Cp(f,t)})}function Uo(e,t,n){return{instance:e,listener:t,currentTarget:n}}function rs(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=jo(e,n),i!=null&&r.unshift(Uo(e,i,o)),i=jo(e,t),i!=null&&r.push(Uo(e,i,o))),e=e.return}return r}function hr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Hc(e,t,n,r,o){for(var i=t._reactName,s=[];n!==null&&n!==r;){var l=n,a=l.alternate,u=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&u!==null&&(l=u,o?(a=jo(n,i),a!=null&&s.unshift(Uo(n,a,l))):o||(a=jo(n,i),a!=null&&s.push(Uo(n,a,l)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var ny=/\r\n?/g,ry=/\u0000|\uFFFD/g;function Qc(e){return(typeof e=="string"?e:""+e).replace(ny,`
`).replace(ry,"")}function Ci(e,t,n){if(t=Qc(t),Qc(e)!==t&&n)throw Error(O(425))}function os(){}var aa=null,ua=null;function ca(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var da=typeof setTimeout=="function"?setTimeout:void 0,oy=typeof clearTimeout=="function"?clearTimeout:void 0,Kc=typeof Promise=="function"?Promise:void 0,iy=typeof queueMicrotask=="function"?queueMicrotask:typeof Kc<"u"?function(e){return Kc.resolve(null).then(e).catch(sy)}:da;function sy(e){setTimeout(function(){throw e})}function kl(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),Io(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);Io(t)}function Cn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Yc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var to=Math.random().toString(36).slice(2),Rt="__reactFiber$"+to,Bo="__reactProps$"+to,Ht="__reactContainer$"+to,fa="__reactEvents$"+to,ly="__reactListeners$"+to,ay="__reactHandles$"+to;function Bn(e){var t=e[Rt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Ht]||n[Rt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Yc(e);e!==null;){if(n=e[Rt])return n;e=Yc(e)}return t}e=n,n=e.parentNode}return null}function oi(e){return e=e[Rt]||e[Ht],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function xr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(O(33))}function As(e){return e[Bo]||null}var pa=[],Sr=-1;function Mn(e){return{current:e}}function ie(e){0>Sr||(e.current=pa[Sr],pa[Sr]=null,Sr--)}function ne(e,t){Sr++,pa[Sr]=e.current,e.current=t}var Rn={},Oe=Mn(Rn),$e=Mn(!1),Jn=Rn;function Hr(e,t){var n=e.type.contextTypes;if(!n)return Rn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Ue(e){return e=e.childContextTypes,e!=null}function is(){ie($e),ie(Oe)}function Gc(e,t,n){if(Oe.current!==Rn)throw Error(O(168));ne(Oe,t),ne($e,n)}function Pp(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(O(108,Qg(e)||"Unknown",o));return ce({},n,r)}function ss(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Rn,Jn=Oe.current,ne(Oe,e),ne($e,$e.current),!0}function Xc(e,t,n){var r=e.stateNode;if(!r)throw Error(O(169));n?(e=Pp(e,t,Jn),r.__reactInternalMemoizedMergedChildContext=e,ie($e),ie(Oe),ne(Oe,e)):ie($e),ne($e,n)}var Ft=null,Ls=!1,Pl=!1;function bp(e){Ft===null?Ft=[e]:Ft.push(e)}function uy(e){Ls=!0,bp(e)}function Dn(){if(!Pl&&Ft!==null){Pl=!0;var e=0,t=ee;try{var n=Ft;for(ee=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Ft=null,Ls=!1}catch(o){throw Ft!==null&&(Ft=Ft.slice(e+1)),qf(au,Dn),o}finally{ee=t,Pl=!1}}return null}var Er=[],Cr=0,ls=null,as=0,tt=[],nt=0,er=null,Ut=1,Bt="";function $n(e,t){Er[Cr++]=as,Er[Cr++]=ls,ls=e,as=t}function Tp(e,t,n){tt[nt++]=Ut,tt[nt++]=Bt,tt[nt++]=er,er=e;var r=Ut;e=Bt;var o=32-yt(r)-1;r&=~(1<<o),n+=1;var i=32-yt(t)+o;if(30<i){var s=o-o%5;i=(r&(1<<s)-1).toString(32),r>>=s,o-=s,Ut=1<<32-yt(t)+o|n<<o|r,Bt=i+e}else Ut=1<<i|n<<o|r,Bt=e}function vu(e){e.return!==null&&($n(e,1),Tp(e,1,0))}function yu(e){for(;e===ls;)ls=Er[--Cr],Er[Cr]=null,as=Er[--Cr],Er[Cr]=null;for(;e===er;)er=tt[--nt],tt[nt]=null,Bt=tt[--nt],tt[nt]=null,Ut=tt[--nt],tt[nt]=null}var Ye=null,Ke=null,le=!1,vt=null;function Np(e,t){var n=rt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function qc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ye=e,Ke=Cn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ye=e,Ke=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=er!==null?{id:Ut,overflow:Bt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=rt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ye=e,Ke=null,!0):!1;default:return!1}}function ha(e){return(e.mode&1)!==0&&(e.flags&128)===0}function ma(e){if(le){var t=Ke;if(t){var n=t;if(!qc(e,t)){if(ha(e))throw Error(O(418));t=Cn(n.nextSibling);var r=Ye;t&&qc(e,t)?Np(r,n):(e.flags=e.flags&-4097|2,le=!1,Ye=e)}}else{if(ha(e))throw Error(O(418));e.flags=e.flags&-4097|2,le=!1,Ye=e}}}function Zc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ye=e}function ki(e){if(e!==Ye)return!1;if(!le)return Zc(e),le=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!ca(e.type,e.memoizedProps)),t&&(t=Ke)){if(ha(e))throw Rp(),Error(O(418));for(;t;)Np(e,t),t=Cn(t.nextSibling)}if(Zc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(O(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ke=Cn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ke=null}}else Ke=Ye?Cn(e.stateNode.nextSibling):null;return!0}function Rp(){for(var e=Ke;e;)e=Cn(e.nextSibling)}function Qr(){Ke=Ye=null,le=!1}function wu(e){vt===null?vt=[e]:vt.push(e)}var cy=Xt.ReactCurrentBatchConfig;function po(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(O(309));var r=n.stateNode}if(!r)throw Error(O(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(s){var l=o.refs;s===null?delete l[i]:l[i]=s},t._stringRef=i,t)}if(typeof e!="string")throw Error(O(284));if(!n._owner)throw Error(O(290,e))}return e}function Pi(e,t){throw e=Object.prototype.toString.call(t),Error(O(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Jc(e){var t=e._init;return t(e._payload)}function Op(e){function t(m,p){if(e){var g=m.deletions;g===null?(m.deletions=[p],m.flags|=16):g.push(p)}}function n(m,p){if(!e)return null;for(;p!==null;)t(m,p),p=p.sibling;return null}function r(m,p){for(m=new Map;p!==null;)p.key!==null?m.set(p.key,p):m.set(p.index,p),p=p.sibling;return m}function o(m,p){return m=Tn(m,p),m.index=0,m.sibling=null,m}function i(m,p,g){return m.index=g,e?(g=m.alternate,g!==null?(g=g.index,g<p?(m.flags|=2,p):g):(m.flags|=2,p)):(m.flags|=1048576,p)}function s(m){return e&&m.alternate===null&&(m.flags|=2),m}function l(m,p,g,S){return p===null||p.tag!==6?(p=Al(g,m.mode,S),p.return=m,p):(p=o(p,g),p.return=m,p)}function a(m,p,g,S){var C=g.type;return C===gr?c(m,p,g.props.children,S,g.key):p!==null&&(p.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===an&&Jc(C)===p.type)?(S=o(p,g.props),S.ref=po(m,p,g),S.return=m,S):(S=Qi(g.type,g.key,g.props,null,m.mode,S),S.ref=po(m,p,g),S.return=m,S)}function u(m,p,g,S){return p===null||p.tag!==4||p.stateNode.containerInfo!==g.containerInfo||p.stateNode.implementation!==g.implementation?(p=Ll(g,m.mode,S),p.return=m,p):(p=o(p,g.children||[]),p.return=m,p)}function c(m,p,g,S,C){return p===null||p.tag!==7?(p=Zn(g,m.mode,S,C),p.return=m,p):(p=o(p,g),p.return=m,p)}function f(m,p,g){if(typeof p=="string"&&p!==""||typeof p=="number")return p=Al(""+p,m.mode,g),p.return=m,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case hi:return g=Qi(p.type,p.key,p.props,null,m.mode,g),g.ref=po(m,null,p),g.return=m,g;case mr:return p=Ll(p,m.mode,g),p.return=m,p;case an:var S=p._init;return f(m,S(p._payload),g)}if(wo(p)||lo(p))return p=Zn(p,m.mode,g,null),p.return=m,p;Pi(m,p)}return null}function h(m,p,g,S){var C=p!==null?p.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return C!==null?null:l(m,p,""+g,S);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case hi:return g.key===C?a(m,p,g,S):null;case mr:return g.key===C?u(m,p,g,S):null;case an:return C=g._init,h(m,p,C(g._payload),S)}if(wo(g)||lo(g))return C!==null?null:c(m,p,g,S,null);Pi(m,g)}return null}function d(m,p,g,S,C){if(typeof S=="string"&&S!==""||typeof S=="number")return m=m.get(g)||null,l(p,m,""+S,C);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case hi:return m=m.get(S.key===null?g:S.key)||null,a(p,m,S,C);case mr:return m=m.get(S.key===null?g:S.key)||null,u(p,m,S,C);case an:var k=S._init;return d(m,p,g,k(S._payload),C)}if(wo(S)||lo(S))return m=m.get(g)||null,c(p,m,S,C,null);Pi(p,S)}return null}function w(m,p,g,S){for(var C=null,k=null,P=p,R=p=0,L=null;P!==null&&R<g.length;R++){P.index>R?(L=P,P=null):L=P.sibling;var A=h(m,P,g[R],S);if(A===null){P===null&&(P=L);break}e&&P&&A.alternate===null&&t(m,P),p=i(A,p,R),k===null?C=A:k.sibling=A,k=A,P=L}if(R===g.length)return n(m,P),le&&$n(m,R),C;if(P===null){for(;R<g.length;R++)P=f(m,g[R],S),P!==null&&(p=i(P,p,R),k===null?C=P:k.sibling=P,k=P);return le&&$n(m,R),C}for(P=r(m,P);R<g.length;R++)L=d(P,m,R,g[R],S),L!==null&&(e&&L.alternate!==null&&P.delete(L.key===null?R:L.key),p=i(L,p,R),k===null?C=L:k.sibling=L,k=L);return e&&P.forEach(function(F){return t(m,F)}),le&&$n(m,R),C}function v(m,p,g,S){var C=lo(g);if(typeof C!="function")throw Error(O(150));if(g=C.call(g),g==null)throw Error(O(151));for(var k=C=null,P=p,R=p=0,L=null,A=g.next();P!==null&&!A.done;R++,A=g.next()){P.index>R?(L=P,P=null):L=P.sibling;var F=h(m,P,A.value,S);if(F===null){P===null&&(P=L);break}e&&P&&F.alternate===null&&t(m,P),p=i(F,p,R),k===null?C=F:k.sibling=F,k=F,P=L}if(A.done)return n(m,P),le&&$n(m,R),C;if(P===null){for(;!A.done;R++,A=g.next())A=f(m,A.value,S),A!==null&&(p=i(A,p,R),k===null?C=A:k.sibling=A,k=A);return le&&$n(m,R),C}for(P=r(m,P);!A.done;R++,A=g.next())A=d(P,m,R,A.value,S),A!==null&&(e&&A.alternate!==null&&P.delete(A.key===null?R:A.key),p=i(A,p,R),k===null?C=A:k.sibling=A,k=A);return e&&P.forEach(function(I){return t(m,I)}),le&&$n(m,R),C}function y(m,p,g,S){if(typeof g=="object"&&g!==null&&g.type===gr&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case hi:e:{for(var C=g.key,k=p;k!==null;){if(k.key===C){if(C=g.type,C===gr){if(k.tag===7){n(m,k.sibling),p=o(k,g.props.children),p.return=m,m=p;break e}}else if(k.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===an&&Jc(C)===k.type){n(m,k.sibling),p=o(k,g.props),p.ref=po(m,k,g),p.return=m,m=p;break e}n(m,k);break}else t(m,k);k=k.sibling}g.type===gr?(p=Zn(g.props.children,m.mode,S,g.key),p.return=m,m=p):(S=Qi(g.type,g.key,g.props,null,m.mode,S),S.ref=po(m,p,g),S.return=m,m=S)}return s(m);case mr:e:{for(k=g.key;p!==null;){if(p.key===k)if(p.tag===4&&p.stateNode.containerInfo===g.containerInfo&&p.stateNode.implementation===g.implementation){n(m,p.sibling),p=o(p,g.children||[]),p.return=m,m=p;break e}else{n(m,p);break}else t(m,p);p=p.sibling}p=Ll(g,m.mode,S),p.return=m,m=p}return s(m);case an:return k=g._init,y(m,p,k(g._payload),S)}if(wo(g))return w(m,p,g,S);if(lo(g))return v(m,p,g,S);Pi(m,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,p!==null&&p.tag===6?(n(m,p.sibling),p=o(p,g),p.return=m,m=p):(n(m,p),p=Al(g,m.mode,S),p.return=m,m=p),s(m)):n(m,p)}return y}var Kr=Op(!0),_p=Op(!1),us=Mn(null),cs=null,kr=null,xu=null;function Su(){xu=kr=cs=null}function Eu(e){var t=us.current;ie(us),e._currentValue=t}function ga(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function _r(e,t){cs=e,xu=kr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Fe=!0),e.firstContext=null)}function it(e){var t=e._currentValue;if(xu!==e)if(e={context:e,memoizedValue:t,next:null},kr===null){if(cs===null)throw Error(O(308));kr=e,cs.dependencies={lanes:0,firstContext:e}}else kr=kr.next=e;return t}var Vn=null;function Cu(e){Vn===null?Vn=[e]:Vn.push(e)}function Ap(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,Cu(t)):(n.next=o.next,o.next=n),t.interleaved=n,Qt(e,r)}function Qt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var un=!1;function ku(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Lp(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Vt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function kn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,q&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Qt(e,n)}return o=r.interleaved,o===null?(t.next=t,Cu(r)):(t.next=o.next,o.next=t),r.interleaved=t,Qt(e,n)}function $i(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,uu(e,n)}}function ed(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=s:i=i.next=s,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function ds(e,t,n,r){var o=e.updateQueue;un=!1;var i=o.firstBaseUpdate,s=o.lastBaseUpdate,l=o.shared.pending;if(l!==null){o.shared.pending=null;var a=l,u=a.next;a.next=null,s===null?i=u:s.next=u,s=a;var c=e.alternate;c!==null&&(c=c.updateQueue,l=c.lastBaseUpdate,l!==s&&(l===null?c.firstBaseUpdate=u:l.next=u,c.lastBaseUpdate=a))}if(i!==null){var f=o.baseState;s=0,c=u=a=null,l=i;do{var h=l.lane,d=l.eventTime;if((r&h)===h){c!==null&&(c=c.next={eventTime:d,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var w=e,v=l;switch(h=t,d=n,v.tag){case 1:if(w=v.payload,typeof w=="function"){f=w.call(d,f,h);break e}f=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=v.payload,h=typeof w=="function"?w.call(d,f,h):w,h==null)break e;f=ce({},f,h);break e;case 2:un=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,h=o.effects,h===null?o.effects=[l]:h.push(l))}else d={eventTime:d,lane:h,tag:l.tag,payload:l.payload,callback:l.callback,next:null},c===null?(u=c=d,a=f):c=c.next=d,s|=h;if(l=l.next,l===null){if(l=o.shared.pending,l===null)break;h=l,l=h.next,h.next=null,o.lastBaseUpdate=h,o.shared.pending=null}}while(!0);if(c===null&&(a=f),o.baseState=a,o.firstBaseUpdate=u,o.lastBaseUpdate=c,t=o.shared.interleaved,t!==null){o=t;do s|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);nr|=s,e.lanes=s,e.memoizedState=f}}function td(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(O(191,o));o.call(r)}}}var ii={},At=Mn(ii),Vo=Mn(ii),Wo=Mn(ii);function Wn(e){if(e===ii)throw Error(O(174));return e}function Pu(e,t){switch(ne(Wo,t),ne(Vo,e),ne(At,ii),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Xl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Xl(t,e)}ie(At),ne(At,t)}function Yr(){ie(At),ie(Vo),ie(Wo)}function jp(e){Wn(Wo.current);var t=Wn(At.current),n=Xl(t,e.type);t!==n&&(ne(Vo,e),ne(At,n))}function bu(e){Vo.current===e&&(ie(At),ie(Vo))}var ae=Mn(0);function fs(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var bl=[];function Tu(){for(var e=0;e<bl.length;e++)bl[e]._workInProgressVersionPrimary=null;bl.length=0}var Ui=Xt.ReactCurrentDispatcher,Tl=Xt.ReactCurrentBatchConfig,tr=0,ue=null,ge=null,we=null,ps=!1,To=!1,Ho=0,dy=0;function be(){throw Error(O(321))}function Nu(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!xt(e[n],t[n]))return!1;return!0}function Ru(e,t,n,r,o,i){if(tr=i,ue=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Ui.current=e===null||e.memoizedState===null?my:gy,e=n(r,o),To){i=0;do{if(To=!1,Ho=0,25<=i)throw Error(O(301));i+=1,we=ge=null,t.updateQueue=null,Ui.current=vy,e=n(r,o)}while(To)}if(Ui.current=hs,t=ge!==null&&ge.next!==null,tr=0,we=ge=ue=null,ps=!1,t)throw Error(O(300));return e}function Ou(){var e=Ho!==0;return Ho=0,e}function Pt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return we===null?ue.memoizedState=we=e:we=we.next=e,we}function st(){if(ge===null){var e=ue.alternate;e=e!==null?e.memoizedState:null}else e=ge.next;var t=we===null?ue.memoizedState:we.next;if(t!==null)we=t,ge=e;else{if(e===null)throw Error(O(310));ge=e,e={memoizedState:ge.memoizedState,baseState:ge.baseState,baseQueue:ge.baseQueue,queue:ge.queue,next:null},we===null?ue.memoizedState=we=e:we=we.next=e}return we}function Qo(e,t){return typeof t=="function"?t(e):t}function Nl(e){var t=st(),n=t.queue;if(n===null)throw Error(O(311));n.lastRenderedReducer=e;var r=ge,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var s=o.next;o.next=i.next,i.next=s}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var l=s=null,a=null,u=i;do{var c=u.lane;if((tr&c)===c)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(l=a=f,s=r):a=a.next=f,ue.lanes|=c,nr|=c}u=u.next}while(u!==null&&u!==i);a===null?s=r:a.next=l,xt(r,t.memoizedState)||(Fe=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,ue.lanes|=i,nr|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Rl(e){var t=st(),n=t.queue;if(n===null)throw Error(O(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var s=o=o.next;do i=e(i,s.action),s=s.next;while(s!==o);xt(i,t.memoizedState)||(Fe=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Mp(){}function Dp(e,t){var n=ue,r=st(),o=t(),i=!xt(r.memoizedState,o);if(i&&(r.memoizedState=o,Fe=!0),r=r.queue,_u(Fp.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||we!==null&&we.memoizedState.tag&1){if(n.flags|=2048,Ko(9,zp.bind(null,n,r,o,t),void 0,null),xe===null)throw Error(O(349));tr&30||Ip(n,t,o)}return o}function Ip(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ue.updateQueue,t===null?(t={lastEffect:null,stores:null},ue.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function zp(e,t,n,r){t.value=n,t.getSnapshot=r,$p(t)&&Up(e)}function Fp(e,t,n){return n(function(){$p(t)&&Up(e)})}function $p(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!xt(e,n)}catch{return!0}}function Up(e){var t=Qt(e,1);t!==null&&wt(t,e,1,-1)}function nd(e){var t=Pt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Qo,lastRenderedState:e},t.queue=e,e=e.dispatch=hy.bind(null,ue,e),[t.memoizedState,e]}function Ko(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=ue.updateQueue,t===null?(t={lastEffect:null,stores:null},ue.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Bp(){return st().memoizedState}function Bi(e,t,n,r){var o=Pt();ue.flags|=e,o.memoizedState=Ko(1|t,n,void 0,r===void 0?null:r)}function js(e,t,n,r){var o=st();r=r===void 0?null:r;var i=void 0;if(ge!==null){var s=ge.memoizedState;if(i=s.destroy,r!==null&&Nu(r,s.deps)){o.memoizedState=Ko(t,n,i,r);return}}ue.flags|=e,o.memoizedState=Ko(1|t,n,i,r)}function rd(e,t){return Bi(8390656,8,e,t)}function _u(e,t){return js(2048,8,e,t)}function Vp(e,t){return js(4,2,e,t)}function Wp(e,t){return js(4,4,e,t)}function Hp(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Qp(e,t,n){return n=n!=null?n.concat([e]):null,js(4,4,Hp.bind(null,t,e),n)}function Au(){}function Kp(e,t){var n=st();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Nu(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Yp(e,t){var n=st();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Nu(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Gp(e,t,n){return tr&21?(xt(n,t)||(n=ep(),ue.lanes|=n,nr|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Fe=!0),e.memoizedState=n)}function fy(e,t){var n=ee;ee=n!==0&&4>n?n:4,e(!0);var r=Tl.transition;Tl.transition={};try{e(!1),t()}finally{ee=n,Tl.transition=r}}function Xp(){return st().memoizedState}function py(e,t,n){var r=bn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},qp(e))Zp(t,n);else if(n=Ap(e,t,n,r),n!==null){var o=je();wt(n,e,r,o),Jp(n,t,r)}}function hy(e,t,n){var r=bn(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(qp(e))Zp(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var s=t.lastRenderedState,l=i(s,n);if(o.hasEagerState=!0,o.eagerState=l,xt(l,s)){var a=t.interleaved;a===null?(o.next=o,Cu(t)):(o.next=a.next,a.next=o),t.interleaved=o;return}}catch{}finally{}n=Ap(e,t,o,r),n!==null&&(o=je(),wt(n,e,r,o),Jp(n,t,r))}}function qp(e){var t=e.alternate;return e===ue||t!==null&&t===ue}function Zp(e,t){To=ps=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Jp(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,uu(e,n)}}var hs={readContext:it,useCallback:be,useContext:be,useEffect:be,useImperativeHandle:be,useInsertionEffect:be,useLayoutEffect:be,useMemo:be,useReducer:be,useRef:be,useState:be,useDebugValue:be,useDeferredValue:be,useTransition:be,useMutableSource:be,useSyncExternalStore:be,useId:be,unstable_isNewReconciler:!1},my={readContext:it,useCallback:function(e,t){return Pt().memoizedState=[e,t===void 0?null:t],e},useContext:it,useEffect:rd,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Bi(4194308,4,Hp.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Bi(4194308,4,e,t)},useInsertionEffect:function(e,t){return Bi(4,2,e,t)},useMemo:function(e,t){var n=Pt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Pt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=py.bind(null,ue,e),[r.memoizedState,e]},useRef:function(e){var t=Pt();return e={current:e},t.memoizedState=e},useState:nd,useDebugValue:Au,useDeferredValue:function(e){return Pt().memoizedState=e},useTransition:function(){var e=nd(!1),t=e[0];return e=fy.bind(null,e[1]),Pt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ue,o=Pt();if(le){if(n===void 0)throw Error(O(407));n=n()}else{if(n=t(),xe===null)throw Error(O(349));tr&30||Ip(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,rd(Fp.bind(null,r,i,e),[e]),r.flags|=2048,Ko(9,zp.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=Pt(),t=xe.identifierPrefix;if(le){var n=Bt,r=Ut;n=(r&~(1<<32-yt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Ho++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=dy++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},gy={readContext:it,useCallback:Kp,useContext:it,useEffect:_u,useImperativeHandle:Qp,useInsertionEffect:Vp,useLayoutEffect:Wp,useMemo:Yp,useReducer:Nl,useRef:Bp,useState:function(){return Nl(Qo)},useDebugValue:Au,useDeferredValue:function(e){var t=st();return Gp(t,ge.memoizedState,e)},useTransition:function(){var e=Nl(Qo)[0],t=st().memoizedState;return[e,t]},useMutableSource:Mp,useSyncExternalStore:Dp,useId:Xp,unstable_isNewReconciler:!1},vy={readContext:it,useCallback:Kp,useContext:it,useEffect:_u,useImperativeHandle:Qp,useInsertionEffect:Vp,useLayoutEffect:Wp,useMemo:Yp,useReducer:Rl,useRef:Bp,useState:function(){return Rl(Qo)},useDebugValue:Au,useDeferredValue:function(e){var t=st();return ge===null?t.memoizedState=e:Gp(t,ge.memoizedState,e)},useTransition:function(){var e=Rl(Qo)[0],t=st().memoizedState;return[e,t]},useMutableSource:Mp,useSyncExternalStore:Dp,useId:Xp,unstable_isNewReconciler:!1};function ft(e,t){if(e&&e.defaultProps){t=ce({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function va(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:ce({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Ms={isMounted:function(e){return(e=e._reactInternals)?sr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=je(),o=bn(e),i=Vt(r,o);i.payload=t,n!=null&&(i.callback=n),t=kn(e,i,o),t!==null&&(wt(t,e,o,r),$i(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=je(),o=bn(e),i=Vt(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=kn(e,i,o),t!==null&&(wt(t,e,o,r),$i(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=je(),r=bn(e),o=Vt(n,r);o.tag=2,t!=null&&(o.callback=t),t=kn(e,o,r),t!==null&&(wt(t,e,r,n),$i(t,e,r))}};function od(e,t,n,r,o,i,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,s):t.prototype&&t.prototype.isPureReactComponent?!Fo(n,r)||!Fo(o,i):!0}function eh(e,t,n){var r=!1,o=Rn,i=t.contextType;return typeof i=="object"&&i!==null?i=it(i):(o=Ue(t)?Jn:Oe.current,r=t.contextTypes,i=(r=r!=null)?Hr(e,o):Rn),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Ms,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function id(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Ms.enqueueReplaceState(t,t.state,null)}function ya(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},ku(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=it(i):(i=Ue(t)?Jn:Oe.current,o.context=Hr(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(va(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&Ms.enqueueReplaceState(o,o.state,null),ds(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function Gr(e,t){try{var n="",r=t;do n+=Hg(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function Ol(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function wa(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var yy=typeof WeakMap=="function"?WeakMap:Map;function th(e,t,n){n=Vt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){gs||(gs=!0,Ra=r),wa(e,t)},n}function nh(e,t,n){n=Vt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){wa(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){wa(e,t),typeof r!="function"&&(Pn===null?Pn=new Set([this]):Pn.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function sd(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new yy;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=Ay.bind(null,e,t,n),t.then(e,e))}function ld(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function ad(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Vt(-1,1),t.tag=2,kn(n,t,1))),n.lanes|=1),e)}var wy=Xt.ReactCurrentOwner,Fe=!1;function Ae(e,t,n,r){t.child=e===null?_p(t,null,n,r):Kr(t,e.child,n,r)}function ud(e,t,n,r,o){n=n.render;var i=t.ref;return _r(t,o),r=Ru(e,t,n,r,i,o),n=Ou(),e!==null&&!Fe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Kt(e,t,o)):(le&&n&&vu(t),t.flags|=1,Ae(e,t,r,o),t.child)}function cd(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!$u(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,rh(e,t,i,r,o)):(e=Qi(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var s=i.memoizedProps;if(n=n.compare,n=n!==null?n:Fo,n(s,r)&&e.ref===t.ref)return Kt(e,t,o)}return t.flags|=1,e=Tn(i,r),e.ref=t.ref,e.return=t,t.child=e}function rh(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(Fo(i,r)&&e.ref===t.ref)if(Fe=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(Fe=!0);else return t.lanes=e.lanes,Kt(e,t,o)}return xa(e,t,n,r,o)}function oh(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ne(br,He),He|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ne(br,He),He|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,ne(br,He),He|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,ne(br,He),He|=r;return Ae(e,t,o,n),t.child}function ih(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function xa(e,t,n,r,o){var i=Ue(n)?Jn:Oe.current;return i=Hr(t,i),_r(t,o),n=Ru(e,t,n,r,i,o),r=Ou(),e!==null&&!Fe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Kt(e,t,o)):(le&&r&&vu(t),t.flags|=1,Ae(e,t,n,o),t.child)}function dd(e,t,n,r,o){if(Ue(n)){var i=!0;ss(t)}else i=!1;if(_r(t,o),t.stateNode===null)Vi(e,t),eh(t,n,r),ya(t,n,r,o),r=!0;else if(e===null){var s=t.stateNode,l=t.memoizedProps;s.props=l;var a=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=it(u):(u=Ue(n)?Jn:Oe.current,u=Hr(t,u));var c=n.getDerivedStateFromProps,f=typeof c=="function"||typeof s.getSnapshotBeforeUpdate=="function";f||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==r||a!==u)&&id(t,s,r,u),un=!1;var h=t.memoizedState;s.state=h,ds(t,r,s,o),a=t.memoizedState,l!==r||h!==a||$e.current||un?(typeof c=="function"&&(va(t,n,c,r),a=t.memoizedState),(l=un||od(t,n,l,r,h,a,u))?(f||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),s.props=r,s.state=a,s.context=u,r=l):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,Lp(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:ft(t.type,l),s.props=u,f=t.pendingProps,h=s.context,a=n.contextType,typeof a=="object"&&a!==null?a=it(a):(a=Ue(n)?Jn:Oe.current,a=Hr(t,a));var d=n.getDerivedStateFromProps;(c=typeof d=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==f||h!==a)&&id(t,s,r,a),un=!1,h=t.memoizedState,s.state=h,ds(t,r,s,o);var w=t.memoizedState;l!==f||h!==w||$e.current||un?(typeof d=="function"&&(va(t,n,d,r),w=t.memoizedState),(u=un||od(t,n,u,r,h,w,a)||!1)?(c||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,w,a),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,w,a)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=w),s.props=r,s.state=w,s.context=a,r=u):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),r=!1)}return Sa(e,t,n,r,i,o)}function Sa(e,t,n,r,o,i){ih(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return o&&Xc(t,n,!1),Kt(e,t,i);r=t.stateNode,wy.current=t;var l=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=Kr(t,e.child,null,i),t.child=Kr(t,null,l,i)):Ae(e,t,l,i),t.memoizedState=r.state,o&&Xc(t,n,!0),t.child}function sh(e){var t=e.stateNode;t.pendingContext?Gc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Gc(e,t.context,!1),Pu(e,t.containerInfo)}function fd(e,t,n,r,o){return Qr(),wu(o),t.flags|=256,Ae(e,t,n,r),t.child}var Ea={dehydrated:null,treeContext:null,retryLane:0};function Ca(e){return{baseLanes:e,cachePool:null,transitions:null}}function lh(e,t,n){var r=t.pendingProps,o=ae.current,i=!1,s=(t.flags&128)!==0,l;if((l=s)||(l=e!==null&&e.memoizedState===null?!1:(o&2)!==0),l?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),ne(ae,o&1),e===null)return ma(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,i?(r=t.mode,i=t.child,s={mode:"hidden",children:s},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=s):i=zs(s,r,0,null),e=Zn(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Ca(n),t.memoizedState=Ea,e):Lu(t,s));if(o=e.memoizedState,o!==null&&(l=o.dehydrated,l!==null))return xy(e,t,s,r,l,o,n);if(i){i=r.fallback,s=t.mode,o=e.child,l=o.sibling;var a={mode:"hidden",children:r.children};return!(s&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Tn(o,a),r.subtreeFlags=o.subtreeFlags&14680064),l!==null?i=Tn(l,i):(i=Zn(i,s,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,s=e.child.memoizedState,s=s===null?Ca(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=Ea,r}return i=e.child,e=i.sibling,r=Tn(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Lu(e,t){return t=zs({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function bi(e,t,n,r){return r!==null&&wu(r),Kr(t,e.child,null,n),e=Lu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function xy(e,t,n,r,o,i,s){if(n)return t.flags&256?(t.flags&=-257,r=Ol(Error(O(422))),bi(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=zs({mode:"visible",children:r.children},o,0,null),i=Zn(i,o,s,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&Kr(t,e.child,null,s),t.child.memoizedState=Ca(s),t.memoizedState=Ea,i);if(!(t.mode&1))return bi(e,t,s,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var l=r.dgst;return r=l,i=Error(O(419)),r=Ol(i,r,void 0),bi(e,t,s,r)}if(l=(s&e.childLanes)!==0,Fe||l){if(r=xe,r!==null){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|s)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,Qt(e,o),wt(r,e,o,-1))}return Fu(),r=Ol(Error(O(421))),bi(e,t,s,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=Ly.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,Ke=Cn(o.nextSibling),Ye=t,le=!0,vt=null,e!==null&&(tt[nt++]=Ut,tt[nt++]=Bt,tt[nt++]=er,Ut=e.id,Bt=e.overflow,er=t),t=Lu(t,r.children),t.flags|=4096,t)}function pd(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),ga(e.return,t,n)}function _l(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function ah(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(Ae(e,t,r.children,n),r=ae.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&pd(e,n,t);else if(e.tag===19)pd(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(ne(ae,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&fs(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),_l(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&fs(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}_l(t,!0,n,null,i);break;case"together":_l(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Vi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Kt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),nr|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(O(153));if(t.child!==null){for(e=t.child,n=Tn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Tn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Sy(e,t,n){switch(t.tag){case 3:sh(t),Qr();break;case 5:jp(t);break;case 1:Ue(t.type)&&ss(t);break;case 4:Pu(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;ne(us,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(ne(ae,ae.current&1),t.flags|=128,null):n&t.child.childLanes?lh(e,t,n):(ne(ae,ae.current&1),e=Kt(e,t,n),e!==null?e.sibling:null);ne(ae,ae.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return ah(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),ne(ae,ae.current),r)break;return null;case 22:case 23:return t.lanes=0,oh(e,t,n)}return Kt(e,t,n)}var uh,ka,ch,dh;uh=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};ka=function(){};ch=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,Wn(At.current);var i=null;switch(n){case"input":o=Ql(e,o),r=Ql(e,r),i=[];break;case"select":o=ce({},o,{value:void 0}),r=ce({},r,{value:void 0}),i=[];break;case"textarea":o=Gl(e,o),r=Gl(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=os)}ql(n,r);var s;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var l=o[u];for(s in l)l.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Ao.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var a=r[u];if(l=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&a!==l&&(a!=null||l!=null))if(u==="style")if(l){for(s in l)!l.hasOwnProperty(s)||a&&a.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in a)a.hasOwnProperty(s)&&l[s]!==a[s]&&(n||(n={}),n[s]=a[s])}else n||(i||(i=[]),i.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(i=i||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(i=i||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Ao.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&oe("scroll",e),i||l===a||(i=[])):(i=i||[]).push(u,a))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};dh=function(e,t,n,r){n!==r&&(t.flags|=4)};function ho(e,t){if(!le)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Te(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Ey(e,t,n){var r=t.pendingProps;switch(yu(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Te(t),null;case 1:return Ue(t.type)&&is(),Te(t),null;case 3:return r=t.stateNode,Yr(),ie($e),ie(Oe),Tu(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(ki(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,vt!==null&&(Aa(vt),vt=null))),ka(e,t),Te(t),null;case 5:bu(t);var o=Wn(Wo.current);if(n=t.type,e!==null&&t.stateNode!=null)ch(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(O(166));return Te(t),null}if(e=Wn(At.current),ki(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[Rt]=t,r[Bo]=i,e=(t.mode&1)!==0,n){case"dialog":oe("cancel",r),oe("close",r);break;case"iframe":case"object":case"embed":oe("load",r);break;case"video":case"audio":for(o=0;o<So.length;o++)oe(So[o],r);break;case"source":oe("error",r);break;case"img":case"image":case"link":oe("error",r),oe("load",r);break;case"details":oe("toggle",r);break;case"input":Ec(r,i),oe("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},oe("invalid",r);break;case"textarea":kc(r,i),oe("invalid",r)}ql(n,i),o=null;for(var s in i)if(i.hasOwnProperty(s)){var l=i[s];s==="children"?typeof l=="string"?r.textContent!==l&&(i.suppressHydrationWarning!==!0&&Ci(r.textContent,l,e),o=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(i.suppressHydrationWarning!==!0&&Ci(r.textContent,l,e),o=["children",""+l]):Ao.hasOwnProperty(s)&&l!=null&&s==="onScroll"&&oe("scroll",r)}switch(n){case"input":mi(r),Cc(r,i,!0);break;case"textarea":mi(r),Pc(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=os)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Ff(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[Rt]=t,e[Bo]=r,uh(e,t,!1,!1),t.stateNode=e;e:{switch(s=Zl(n,r),n){case"dialog":oe("cancel",e),oe("close",e),o=r;break;case"iframe":case"object":case"embed":oe("load",e),o=r;break;case"video":case"audio":for(o=0;o<So.length;o++)oe(So[o],e);o=r;break;case"source":oe("error",e),o=r;break;case"img":case"image":case"link":oe("error",e),oe("load",e),o=r;break;case"details":oe("toggle",e),o=r;break;case"input":Ec(e,r),o=Ql(e,r),oe("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=ce({},r,{value:void 0}),oe("invalid",e);break;case"textarea":kc(e,r),o=Gl(e,r),oe("invalid",e);break;default:o=r}ql(n,o),l=o;for(i in l)if(l.hasOwnProperty(i)){var a=l[i];i==="style"?Bf(e,a):i==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&$f(e,a)):i==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&Lo(e,a):typeof a=="number"&&Lo(e,""+a):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Ao.hasOwnProperty(i)?a!=null&&i==="onScroll"&&oe("scroll",e):a!=null&&ru(e,i,a,s))}switch(n){case"input":mi(e),Cc(e,r,!1);break;case"textarea":mi(e),Pc(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Nn(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Tr(e,!!r.multiple,i,!1):r.defaultValue!=null&&Tr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=os)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Te(t),null;case 6:if(e&&t.stateNode!=null)dh(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(O(166));if(n=Wn(Wo.current),Wn(At.current),ki(t)){if(r=t.stateNode,n=t.memoizedProps,r[Rt]=t,(i=r.nodeValue!==n)&&(e=Ye,e!==null))switch(e.tag){case 3:Ci(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ci(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Rt]=t,t.stateNode=r}return Te(t),null;case 13:if(ie(ae),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(le&&Ke!==null&&t.mode&1&&!(t.flags&128))Rp(),Qr(),t.flags|=98560,i=!1;else if(i=ki(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(O(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(O(317));i[Rt]=t}else Qr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Te(t),i=!1}else vt!==null&&(Aa(vt),vt=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||ae.current&1?ye===0&&(ye=3):Fu())),t.updateQueue!==null&&(t.flags|=4),Te(t),null);case 4:return Yr(),ka(e,t),e===null&&$o(t.stateNode.containerInfo),Te(t),null;case 10:return Eu(t.type._context),Te(t),null;case 17:return Ue(t.type)&&is(),Te(t),null;case 19:if(ie(ae),i=t.memoizedState,i===null)return Te(t),null;if(r=(t.flags&128)!==0,s=i.rendering,s===null)if(r)ho(i,!1);else{if(ye!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=fs(e),s!==null){for(t.flags|=128,ho(i,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,s=i.alternate,s===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return ne(ae,ae.current&1|2),t.child}e=e.sibling}i.tail!==null&&pe()>Xr&&(t.flags|=128,r=!0,ho(i,!1),t.lanes=4194304)}else{if(!r)if(e=fs(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),ho(i,!0),i.tail===null&&i.tailMode==="hidden"&&!s.alternate&&!le)return Te(t),null}else 2*pe()-i.renderingStartTime>Xr&&n!==1073741824&&(t.flags|=128,r=!0,ho(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(n=i.last,n!==null?n.sibling=s:t.child=s,i.last=s)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=pe(),t.sibling=null,n=ae.current,ne(ae,r?n&1|2:n&1),t):(Te(t),null);case 22:case 23:return zu(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?He&1073741824&&(Te(t),t.subtreeFlags&6&&(t.flags|=8192)):Te(t),null;case 24:return null;case 25:return null}throw Error(O(156,t.tag))}function Cy(e,t){switch(yu(t),t.tag){case 1:return Ue(t.type)&&is(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Yr(),ie($e),ie(Oe),Tu(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return bu(t),null;case 13:if(ie(ae),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(O(340));Qr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ie(ae),null;case 4:return Yr(),null;case 10:return Eu(t.type._context),null;case 22:case 23:return zu(),null;case 24:return null;default:return null}}var Ti=!1,Re=!1,ky=typeof WeakSet=="function"?WeakSet:Set,D=null;function Pr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){fe(e,t,r)}else n.current=null}function Pa(e,t,n){try{n()}catch(r){fe(e,t,r)}}var hd=!1;function Py(e,t){if(aa=ts,e=gp(),gu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var s=0,l=-1,a=-1,u=0,c=0,f=e,h=null;t:for(;;){for(var d;f!==n||o!==0&&f.nodeType!==3||(l=s+o),f!==i||r!==0&&f.nodeType!==3||(a=s+r),f.nodeType===3&&(s+=f.nodeValue.length),(d=f.firstChild)!==null;)h=f,f=d;for(;;){if(f===e)break t;if(h===n&&++u===o&&(l=s),h===i&&++c===r&&(a=s),(d=f.nextSibling)!==null)break;f=h,h=f.parentNode}f=d}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(ua={focusedElem:e,selectionRange:n},ts=!1,D=t;D!==null;)if(t=D,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,D=e;else for(;D!==null;){t=D;try{var w=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(w!==null){var v=w.memoizedProps,y=w.memoizedState,m=t.stateNode,p=m.getSnapshotBeforeUpdate(t.elementType===t.type?v:ft(t.type,v),y);m.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(O(163))}}catch(S){fe(t,t.return,S)}if(e=t.sibling,e!==null){e.return=t.return,D=e;break}D=t.return}return w=hd,hd=!1,w}function No(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&Pa(t,n,i)}o=o.next}while(o!==r)}}function Ds(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ba(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function fh(e){var t=e.alternate;t!==null&&(e.alternate=null,fh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Rt],delete t[Bo],delete t[fa],delete t[ly],delete t[ay])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ph(e){return e.tag===5||e.tag===3||e.tag===4}function md(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||ph(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ta(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=os));else if(r!==4&&(e=e.child,e!==null))for(Ta(e,t,n),e=e.sibling;e!==null;)Ta(e,t,n),e=e.sibling}function Na(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Na(e,t,n),e=e.sibling;e!==null;)Na(e,t,n),e=e.sibling}var Se=null,gt=!1;function rn(e,t,n){for(n=n.child;n!==null;)hh(e,t,n),n=n.sibling}function hh(e,t,n){if(_t&&typeof _t.onCommitFiberUnmount=="function")try{_t.onCommitFiberUnmount(Ns,n)}catch{}switch(n.tag){case 5:Re||Pr(n,t);case 6:var r=Se,o=gt;Se=null,rn(e,t,n),Se=r,gt=o,Se!==null&&(gt?(e=Se,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Se.removeChild(n.stateNode));break;case 18:Se!==null&&(gt?(e=Se,n=n.stateNode,e.nodeType===8?kl(e.parentNode,n):e.nodeType===1&&kl(e,n),Io(e)):kl(Se,n.stateNode));break;case 4:r=Se,o=gt,Se=n.stateNode.containerInfo,gt=!0,rn(e,t,n),Se=r,gt=o;break;case 0:case 11:case 14:case 15:if(!Re&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,s=i.destroy;i=i.tag,s!==void 0&&(i&2||i&4)&&Pa(n,t,s),o=o.next}while(o!==r)}rn(e,t,n);break;case 1:if(!Re&&(Pr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){fe(n,t,l)}rn(e,t,n);break;case 21:rn(e,t,n);break;case 22:n.mode&1?(Re=(r=Re)||n.memoizedState!==null,rn(e,t,n),Re=r):rn(e,t,n);break;default:rn(e,t,n)}}function gd(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new ky),t.forEach(function(r){var o=jy.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function ct(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,s=t,l=s;e:for(;l!==null;){switch(l.tag){case 5:Se=l.stateNode,gt=!1;break e;case 3:Se=l.stateNode.containerInfo,gt=!0;break e;case 4:Se=l.stateNode.containerInfo,gt=!0;break e}l=l.return}if(Se===null)throw Error(O(160));hh(i,s,o),Se=null,gt=!1;var a=o.alternate;a!==null&&(a.return=null),o.return=null}catch(u){fe(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)mh(t,e),t=t.sibling}function mh(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ct(t,e),kt(e),r&4){try{No(3,e,e.return),Ds(3,e)}catch(v){fe(e,e.return,v)}try{No(5,e,e.return)}catch(v){fe(e,e.return,v)}}break;case 1:ct(t,e),kt(e),r&512&&n!==null&&Pr(n,n.return);break;case 5:if(ct(t,e),kt(e),r&512&&n!==null&&Pr(n,n.return),e.flags&32){var o=e.stateNode;try{Lo(o,"")}catch(v){fe(e,e.return,v)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,s=n!==null?n.memoizedProps:i,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&i.type==="radio"&&i.name!=null&&If(o,i),Zl(l,s);var u=Zl(l,i);for(s=0;s<a.length;s+=2){var c=a[s],f=a[s+1];c==="style"?Bf(o,f):c==="dangerouslySetInnerHTML"?$f(o,f):c==="children"?Lo(o,f):ru(o,c,f,u)}switch(l){case"input":Kl(o,i);break;case"textarea":zf(o,i);break;case"select":var h=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var d=i.value;d!=null?Tr(o,!!i.multiple,d,!1):h!==!!i.multiple&&(i.defaultValue!=null?Tr(o,!!i.multiple,i.defaultValue,!0):Tr(o,!!i.multiple,i.multiple?[]:"",!1))}o[Bo]=i}catch(v){fe(e,e.return,v)}}break;case 6:if(ct(t,e),kt(e),r&4){if(e.stateNode===null)throw Error(O(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(v){fe(e,e.return,v)}}break;case 3:if(ct(t,e),kt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Io(t.containerInfo)}catch(v){fe(e,e.return,v)}break;case 4:ct(t,e),kt(e);break;case 13:ct(t,e),kt(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(Du=pe())),r&4&&gd(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(Re=(u=Re)||c,ct(t,e),Re=u):ct(t,e),kt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(D=e,c=e.child;c!==null;){for(f=D=c;D!==null;){switch(h=D,d=h.child,h.tag){case 0:case 11:case 14:case 15:No(4,h,h.return);break;case 1:Pr(h,h.return);var w=h.stateNode;if(typeof w.componentWillUnmount=="function"){r=h,n=h.return;try{t=r,w.props=t.memoizedProps,w.state=t.memoizedState,w.componentWillUnmount()}catch(v){fe(r,n,v)}}break;case 5:Pr(h,h.return);break;case 22:if(h.memoizedState!==null){yd(f);continue}}d!==null?(d.return=h,D=d):yd(f)}c=c.sibling}e:for(c=null,f=e;;){if(f.tag===5){if(c===null){c=f;try{o=f.stateNode,u?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(l=f.stateNode,a=f.memoizedProps.style,s=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=Uf("display",s))}catch(v){fe(e,e.return,v)}}}else if(f.tag===6){if(c===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(v){fe(e,e.return,v)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;c===f&&(c=null),f=f.return}c===f&&(c=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:ct(t,e),kt(e),r&4&&gd(e);break;case 21:break;default:ct(t,e),kt(e)}}function kt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(ph(n)){var r=n;break e}n=n.return}throw Error(O(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(Lo(o,""),r.flags&=-33);var i=md(e);Na(e,i,o);break;case 3:case 4:var s=r.stateNode.containerInfo,l=md(e);Ta(e,l,s);break;default:throw Error(O(161))}}catch(a){fe(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function by(e,t,n){D=e,gh(e)}function gh(e,t,n){for(var r=(e.mode&1)!==0;D!==null;){var o=D,i=o.child;if(o.tag===22&&r){var s=o.memoizedState!==null||Ti;if(!s){var l=o.alternate,a=l!==null&&l.memoizedState!==null||Re;l=Ti;var u=Re;if(Ti=s,(Re=a)&&!u)for(D=o;D!==null;)s=D,a=s.child,s.tag===22&&s.memoizedState!==null?wd(o):a!==null?(a.return=s,D=a):wd(o);for(;i!==null;)D=i,gh(i),i=i.sibling;D=o,Ti=l,Re=u}vd(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,D=i):vd(e)}}function vd(e){for(;D!==null;){var t=D;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Re||Ds(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Re)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:ft(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&td(t,i,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}td(t,s,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var f=c.dehydrated;f!==null&&Io(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(O(163))}Re||t.flags&512&&ba(t)}catch(h){fe(t,t.return,h)}}if(t===e){D=null;break}if(n=t.sibling,n!==null){n.return=t.return,D=n;break}D=t.return}}function yd(e){for(;D!==null;){var t=D;if(t===e){D=null;break}var n=t.sibling;if(n!==null){n.return=t.return,D=n;break}D=t.return}}function wd(e){for(;D!==null;){var t=D;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Ds(4,t)}catch(a){fe(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(a){fe(t,o,a)}}var i=t.return;try{ba(t)}catch(a){fe(t,i,a)}break;case 5:var s=t.return;try{ba(t)}catch(a){fe(t,s,a)}}}catch(a){fe(t,t.return,a)}if(t===e){D=null;break}var l=t.sibling;if(l!==null){l.return=t.return,D=l;break}D=t.return}}var Ty=Math.ceil,ms=Xt.ReactCurrentDispatcher,ju=Xt.ReactCurrentOwner,ot=Xt.ReactCurrentBatchConfig,q=0,xe=null,me=null,Ee=0,He=0,br=Mn(0),ye=0,Yo=null,nr=0,Is=0,Mu=0,Ro=null,ze=null,Du=0,Xr=1/0,zt=null,gs=!1,Ra=null,Pn=null,Ni=!1,yn=null,vs=0,Oo=0,Oa=null,Wi=-1,Hi=0;function je(){return q&6?pe():Wi!==-1?Wi:Wi=pe()}function bn(e){return e.mode&1?q&2&&Ee!==0?Ee&-Ee:cy.transition!==null?(Hi===0&&(Hi=ep()),Hi):(e=ee,e!==0||(e=window.event,e=e===void 0?16:lp(e.type)),e):1}function wt(e,t,n,r){if(50<Oo)throw Oo=0,Oa=null,Error(O(185));ni(e,n,r),(!(q&2)||e!==xe)&&(e===xe&&(!(q&2)&&(Is|=n),ye===4&&dn(e,Ee)),Be(e,r),n===1&&q===0&&!(t.mode&1)&&(Xr=pe()+500,Ls&&Dn()))}function Be(e,t){var n=e.callbackNode;cv(e,t);var r=es(e,e===xe?Ee:0);if(r===0)n!==null&&Nc(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Nc(n),t===1)e.tag===0?uy(xd.bind(null,e)):bp(xd.bind(null,e)),iy(function(){!(q&6)&&Dn()}),n=null;else{switch(tp(r)){case 1:n=au;break;case 4:n=Zf;break;case 16:n=Ji;break;case 536870912:n=Jf;break;default:n=Ji}n=kh(n,vh.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function vh(e,t){if(Wi=-1,Hi=0,q&6)throw Error(O(327));var n=e.callbackNode;if(Ar()&&e.callbackNode!==n)return null;var r=es(e,e===xe?Ee:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=ys(e,r);else{t=r;var o=q;q|=2;var i=wh();(xe!==e||Ee!==t)&&(zt=null,Xr=pe()+500,qn(e,t));do try{Oy();break}catch(l){yh(e,l)}while(!0);Su(),ms.current=i,q=o,me!==null?t=0:(xe=null,Ee=0,t=ye)}if(t!==0){if(t===2&&(o=ra(e),o!==0&&(r=o,t=_a(e,o))),t===1)throw n=Yo,qn(e,0),dn(e,r),Be(e,pe()),n;if(t===6)dn(e,r);else{if(o=e.current.alternate,!(r&30)&&!Ny(o)&&(t=ys(e,r),t===2&&(i=ra(e),i!==0&&(r=i,t=_a(e,i))),t===1))throw n=Yo,qn(e,0),dn(e,r),Be(e,pe()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(O(345));case 2:Un(e,ze,zt);break;case 3:if(dn(e,r),(r&130023424)===r&&(t=Du+500-pe(),10<t)){if(es(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){je(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=da(Un.bind(null,e,ze,zt),t);break}Un(e,ze,zt);break;case 4:if(dn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var s=31-yt(r);i=1<<s,s=t[s],s>o&&(o=s),r&=~i}if(r=o,r=pe()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Ty(r/1960))-r,10<r){e.timeoutHandle=da(Un.bind(null,e,ze,zt),r);break}Un(e,ze,zt);break;case 5:Un(e,ze,zt);break;default:throw Error(O(329))}}}return Be(e,pe()),e.callbackNode===n?vh.bind(null,e):null}function _a(e,t){var n=Ro;return e.current.memoizedState.isDehydrated&&(qn(e,t).flags|=256),e=ys(e,t),e!==2&&(t=ze,ze=n,t!==null&&Aa(t)),e}function Aa(e){ze===null?ze=e:ze.push.apply(ze,e)}function Ny(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!xt(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function dn(e,t){for(t&=~Mu,t&=~Is,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-yt(t),r=1<<n;e[n]=-1,t&=~r}}function xd(e){if(q&6)throw Error(O(327));Ar();var t=es(e,0);if(!(t&1))return Be(e,pe()),null;var n=ys(e,t);if(e.tag!==0&&n===2){var r=ra(e);r!==0&&(t=r,n=_a(e,r))}if(n===1)throw n=Yo,qn(e,0),dn(e,t),Be(e,pe()),n;if(n===6)throw Error(O(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Un(e,ze,zt),Be(e,pe()),null}function Iu(e,t){var n=q;q|=1;try{return e(t)}finally{q=n,q===0&&(Xr=pe()+500,Ls&&Dn())}}function rr(e){yn!==null&&yn.tag===0&&!(q&6)&&Ar();var t=q;q|=1;var n=ot.transition,r=ee;try{if(ot.transition=null,ee=1,e)return e()}finally{ee=r,ot.transition=n,q=t,!(q&6)&&Dn()}}function zu(){He=br.current,ie(br)}function qn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,oy(n)),me!==null)for(n=me.return;n!==null;){var r=n;switch(yu(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&is();break;case 3:Yr(),ie($e),ie(Oe),Tu();break;case 5:bu(r);break;case 4:Yr();break;case 13:ie(ae);break;case 19:ie(ae);break;case 10:Eu(r.type._context);break;case 22:case 23:zu()}n=n.return}if(xe=e,me=e=Tn(e.current,null),Ee=He=t,ye=0,Yo=null,Mu=Is=nr=0,ze=Ro=null,Vn!==null){for(t=0;t<Vn.length;t++)if(n=Vn[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var s=i.next;i.next=o,r.next=s}n.pending=r}Vn=null}return e}function yh(e,t){do{var n=me;try{if(Su(),Ui.current=hs,ps){for(var r=ue.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}ps=!1}if(tr=0,we=ge=ue=null,To=!1,Ho=0,ju.current=null,n===null||n.return===null){ye=1,Yo=t,me=null;break}e:{var i=e,s=n.return,l=n,a=t;if(t=Ee,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,c=l,f=c.tag;if(!(c.mode&1)&&(f===0||f===11||f===15)){var h=c.alternate;h?(c.updateQueue=h.updateQueue,c.memoizedState=h.memoizedState,c.lanes=h.lanes):(c.updateQueue=null,c.memoizedState=null)}var d=ld(s);if(d!==null){d.flags&=-257,ad(d,s,l,i,t),d.mode&1&&sd(i,u,t),t=d,a=u;var w=t.updateQueue;if(w===null){var v=new Set;v.add(a),t.updateQueue=v}else w.add(a);break e}else{if(!(t&1)){sd(i,u,t),Fu();break e}a=Error(O(426))}}else if(le&&l.mode&1){var y=ld(s);if(y!==null){!(y.flags&65536)&&(y.flags|=256),ad(y,s,l,i,t),wu(Gr(a,l));break e}}i=a=Gr(a,l),ye!==4&&(ye=2),Ro===null?Ro=[i]:Ro.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var m=th(i,a,t);ed(i,m);break e;case 1:l=a;var p=i.type,g=i.stateNode;if(!(i.flags&128)&&(typeof p.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(Pn===null||!Pn.has(g)))){i.flags|=65536,t&=-t,i.lanes|=t;var S=nh(i,l,t);ed(i,S);break e}}i=i.return}while(i!==null)}Sh(n)}catch(C){t=C,me===n&&n!==null&&(me=n=n.return);continue}break}while(!0)}function wh(){var e=ms.current;return ms.current=hs,e===null?hs:e}function Fu(){(ye===0||ye===3||ye===2)&&(ye=4),xe===null||!(nr&268435455)&&!(Is&268435455)||dn(xe,Ee)}function ys(e,t){var n=q;q|=2;var r=wh();(xe!==e||Ee!==t)&&(zt=null,qn(e,t));do try{Ry();break}catch(o){yh(e,o)}while(!0);if(Su(),q=n,ms.current=r,me!==null)throw Error(O(261));return xe=null,Ee=0,ye}function Ry(){for(;me!==null;)xh(me)}function Oy(){for(;me!==null&&!tv();)xh(me)}function xh(e){var t=Ch(e.alternate,e,He);e.memoizedProps=e.pendingProps,t===null?Sh(e):me=t,ju.current=null}function Sh(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Cy(n,t),n!==null){n.flags&=32767,me=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ye=6,me=null;return}}else if(n=Ey(n,t,He),n!==null){me=n;return}if(t=t.sibling,t!==null){me=t;return}me=t=e}while(t!==null);ye===0&&(ye=5)}function Un(e,t,n){var r=ee,o=ot.transition;try{ot.transition=null,ee=1,_y(e,t,n,r)}finally{ot.transition=o,ee=r}return null}function _y(e,t,n,r){do Ar();while(yn!==null);if(q&6)throw Error(O(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(O(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(dv(e,i),e===xe&&(me=xe=null,Ee=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Ni||(Ni=!0,kh(Ji,function(){return Ar(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=ot.transition,ot.transition=null;var s=ee;ee=1;var l=q;q|=4,ju.current=null,Py(e,n),mh(n,e),qv(ua),ts=!!aa,ua=aa=null,e.current=n,by(n),nv(),q=l,ee=s,ot.transition=i}else e.current=n;if(Ni&&(Ni=!1,yn=e,vs=o),i=e.pendingLanes,i===0&&(Pn=null),iv(n.stateNode),Be(e,pe()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(gs)throw gs=!1,e=Ra,Ra=null,e;return vs&1&&e.tag!==0&&Ar(),i=e.pendingLanes,i&1?e===Oa?Oo++:(Oo=0,Oa=e):Oo=0,Dn(),null}function Ar(){if(yn!==null){var e=tp(vs),t=ot.transition,n=ee;try{if(ot.transition=null,ee=16>e?16:e,yn===null)var r=!1;else{if(e=yn,yn=null,vs=0,q&6)throw Error(O(331));var o=q;for(q|=4,D=e.current;D!==null;){var i=D,s=i.child;if(D.flags&16){var l=i.deletions;if(l!==null){for(var a=0;a<l.length;a++){var u=l[a];for(D=u;D!==null;){var c=D;switch(c.tag){case 0:case 11:case 15:No(8,c,i)}var f=c.child;if(f!==null)f.return=c,D=f;else for(;D!==null;){c=D;var h=c.sibling,d=c.return;if(fh(c),c===u){D=null;break}if(h!==null){h.return=d,D=h;break}D=d}}}var w=i.alternate;if(w!==null){var v=w.child;if(v!==null){w.child=null;do{var y=v.sibling;v.sibling=null,v=y}while(v!==null)}}D=i}}if(i.subtreeFlags&2064&&s!==null)s.return=i,D=s;else e:for(;D!==null;){if(i=D,i.flags&2048)switch(i.tag){case 0:case 11:case 15:No(9,i,i.return)}var m=i.sibling;if(m!==null){m.return=i.return,D=m;break e}D=i.return}}var p=e.current;for(D=p;D!==null;){s=D;var g=s.child;if(s.subtreeFlags&2064&&g!==null)g.return=s,D=g;else e:for(s=p;D!==null;){if(l=D,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:Ds(9,l)}}catch(C){fe(l,l.return,C)}if(l===s){D=null;break e}var S=l.sibling;if(S!==null){S.return=l.return,D=S;break e}D=l.return}}if(q=o,Dn(),_t&&typeof _t.onPostCommitFiberRoot=="function")try{_t.onPostCommitFiberRoot(Ns,e)}catch{}r=!0}return r}finally{ee=n,ot.transition=t}}return!1}function Sd(e,t,n){t=Gr(n,t),t=th(e,t,1),e=kn(e,t,1),t=je(),e!==null&&(ni(e,1,t),Be(e,t))}function fe(e,t,n){if(e.tag===3)Sd(e,e,n);else for(;t!==null;){if(t.tag===3){Sd(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Pn===null||!Pn.has(r))){e=Gr(n,e),e=nh(t,e,1),t=kn(t,e,1),e=je(),t!==null&&(ni(t,1,e),Be(t,e));break}}t=t.return}}function Ay(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=je(),e.pingedLanes|=e.suspendedLanes&n,xe===e&&(Ee&n)===n&&(ye===4||ye===3&&(Ee&130023424)===Ee&&500>pe()-Du?qn(e,0):Mu|=n),Be(e,t)}function Eh(e,t){t===0&&(e.mode&1?(t=yi,yi<<=1,!(yi&130023424)&&(yi=4194304)):t=1);var n=je();e=Qt(e,t),e!==null&&(ni(e,t,n),Be(e,n))}function Ly(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Eh(e,n)}function jy(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(O(314))}r!==null&&r.delete(t),Eh(e,n)}var Ch;Ch=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||$e.current)Fe=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Fe=!1,Sy(e,t,n);Fe=!!(e.flags&131072)}else Fe=!1,le&&t.flags&1048576&&Tp(t,as,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Vi(e,t),e=t.pendingProps;var o=Hr(t,Oe.current);_r(t,n),o=Ru(null,t,r,e,o,n);var i=Ou();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ue(r)?(i=!0,ss(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,ku(t),o.updater=Ms,t.stateNode=o,o._reactInternals=t,ya(t,r,e,n),t=Sa(null,t,r,!0,i,n)):(t.tag=0,le&&i&&vu(t),Ae(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Vi(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=Dy(r),e=ft(r,e),o){case 0:t=xa(null,t,r,e,n);break e;case 1:t=dd(null,t,r,e,n);break e;case 11:t=ud(null,t,r,e,n);break e;case 14:t=cd(null,t,r,ft(r.type,e),n);break e}throw Error(O(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ft(r,o),xa(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ft(r,o),dd(e,t,r,o,n);case 3:e:{if(sh(t),e===null)throw Error(O(387));r=t.pendingProps,i=t.memoizedState,o=i.element,Lp(e,t),ds(t,r,null,n);var s=t.memoizedState;if(r=s.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=Gr(Error(O(423)),t),t=fd(e,t,r,n,o);break e}else if(r!==o){o=Gr(Error(O(424)),t),t=fd(e,t,r,n,o);break e}else for(Ke=Cn(t.stateNode.containerInfo.firstChild),Ye=t,le=!0,vt=null,n=_p(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Qr(),r===o){t=Kt(e,t,n);break e}Ae(e,t,r,n)}t=t.child}return t;case 5:return jp(t),e===null&&ma(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,s=o.children,ca(r,o)?s=null:i!==null&&ca(r,i)&&(t.flags|=32),ih(e,t),Ae(e,t,s,n),t.child;case 6:return e===null&&ma(t),null;case 13:return lh(e,t,n);case 4:return Pu(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Kr(t,null,r,n):Ae(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ft(r,o),ud(e,t,r,o,n);case 7:return Ae(e,t,t.pendingProps,n),t.child;case 8:return Ae(e,t,t.pendingProps.children,n),t.child;case 12:return Ae(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,s=o.value,ne(us,r._currentValue),r._currentValue=s,i!==null)if(xt(i.value,s)){if(i.children===o.children&&!$e.current){t=Kt(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var l=i.dependencies;if(l!==null){s=i.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(i.tag===1){a=Vt(-1,n&-n),a.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?a.next=a:(a.next=c.next,c.next=a),u.pending=a}}i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),ga(i.return,n,t),l.lanes|=n;break}a=a.next}}else if(i.tag===10)s=i.type===t.type?null:i.child;else if(i.tag===18){if(s=i.return,s===null)throw Error(O(341));s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),ga(s,n,t),s=i.sibling}else s=i.child;if(s!==null)s.return=i;else for(s=i;s!==null;){if(s===t){s=null;break}if(i=s.sibling,i!==null){i.return=s.return,s=i;break}s=s.return}i=s}Ae(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,_r(t,n),o=it(o),r=r(o),t.flags|=1,Ae(e,t,r,n),t.child;case 14:return r=t.type,o=ft(r,t.pendingProps),o=ft(r.type,o),cd(e,t,r,o,n);case 15:return rh(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ft(r,o),Vi(e,t),t.tag=1,Ue(r)?(e=!0,ss(t)):e=!1,_r(t,n),eh(t,r,o),ya(t,r,o,n),Sa(null,t,r,!0,e,n);case 19:return ah(e,t,n);case 22:return oh(e,t,n)}throw Error(O(156,t.tag))};function kh(e,t){return qf(e,t)}function My(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function rt(e,t,n,r){return new My(e,t,n,r)}function $u(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Dy(e){if(typeof e=="function")return $u(e)?1:0;if(e!=null){if(e=e.$$typeof,e===iu)return 11;if(e===su)return 14}return 2}function Tn(e,t){var n=e.alternate;return n===null?(n=rt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Qi(e,t,n,r,o,i){var s=2;if(r=e,typeof e=="function")$u(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case gr:return Zn(n.children,o,i,t);case ou:s=8,o|=8;break;case Bl:return e=rt(12,n,t,o|2),e.elementType=Bl,e.lanes=i,e;case Vl:return e=rt(13,n,t,o),e.elementType=Vl,e.lanes=i,e;case Wl:return e=rt(19,n,t,o),e.elementType=Wl,e.lanes=i,e;case jf:return zs(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Af:s=10;break e;case Lf:s=9;break e;case iu:s=11;break e;case su:s=14;break e;case an:s=16,r=null;break e}throw Error(O(130,e==null?e:typeof e,""))}return t=rt(s,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function Zn(e,t,n,r){return e=rt(7,e,r,t),e.lanes=n,e}function zs(e,t,n,r){return e=rt(22,e,r,t),e.elementType=jf,e.lanes=n,e.stateNode={isHidden:!1},e}function Al(e,t,n){return e=rt(6,e,null,t),e.lanes=n,e}function Ll(e,t,n){return t=rt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Iy(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=pl(0),this.expirationTimes=pl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=pl(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Uu(e,t,n,r,o,i,s,l,a){return e=new Iy(e,t,n,l,a),t===1?(t=1,i===!0&&(t|=8)):t=0,i=rt(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},ku(i),e}function zy(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:mr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Ph(e){if(!e)return Rn;e=e._reactInternals;e:{if(sr(e)!==e||e.tag!==1)throw Error(O(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ue(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(O(171))}if(e.tag===1){var n=e.type;if(Ue(n))return Pp(e,n,t)}return t}function bh(e,t,n,r,o,i,s,l,a){return e=Uu(n,r,!0,e,o,i,s,l,a),e.context=Ph(null),n=e.current,r=je(),o=bn(n),i=Vt(r,o),i.callback=t??null,kn(n,i,o),e.current.lanes=o,ni(e,o,r),Be(e,r),e}function Fs(e,t,n,r){var o=t.current,i=je(),s=bn(o);return n=Ph(n),t.context===null?t.context=n:t.pendingContext=n,t=Vt(i,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=kn(o,t,s),e!==null&&(wt(e,o,s,i),$i(e,o,s)),s}function ws(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Ed(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Bu(e,t){Ed(e,t),(e=e.alternate)&&Ed(e,t)}function Fy(){return null}var Th=typeof reportError=="function"?reportError:function(e){console.error(e)};function Vu(e){this._internalRoot=e}$s.prototype.render=Vu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(O(409));Fs(e,t,null,null)};$s.prototype.unmount=Vu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;rr(function(){Fs(null,e,null,null)}),t[Ht]=null}};function $s(e){this._internalRoot=e}$s.prototype.unstable_scheduleHydration=function(e){if(e){var t=op();e={blockedOn:null,target:e,priority:t};for(var n=0;n<cn.length&&t!==0&&t<cn[n].priority;n++);cn.splice(n,0,e),n===0&&sp(e)}};function Wu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Us(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Cd(){}function $y(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var u=ws(s);i.call(u)}}var s=bh(t,r,e,0,null,!1,!1,"",Cd);return e._reactRootContainer=s,e[Ht]=s.current,$o(e.nodeType===8?e.parentNode:e),rr(),s}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var l=r;r=function(){var u=ws(a);l.call(u)}}var a=Uu(e,0,!1,null,null,!1,!1,"",Cd);return e._reactRootContainer=a,e[Ht]=a.current,$o(e.nodeType===8?e.parentNode:e),rr(function(){Fs(t,a,n,r)}),a}function Bs(e,t,n,r,o){var i=n._reactRootContainer;if(i){var s=i;if(typeof o=="function"){var l=o;o=function(){var a=ws(s);l.call(a)}}Fs(t,s,e,o)}else s=$y(n,t,e,o,r);return ws(s)}np=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=xo(t.pendingLanes);n!==0&&(uu(t,n|1),Be(t,pe()),!(q&6)&&(Xr=pe()+500,Dn()))}break;case 13:rr(function(){var r=Qt(e,1);if(r!==null){var o=je();wt(r,e,1,o)}}),Bu(e,1)}};cu=function(e){if(e.tag===13){var t=Qt(e,134217728);if(t!==null){var n=je();wt(t,e,134217728,n)}Bu(e,134217728)}};rp=function(e){if(e.tag===13){var t=bn(e),n=Qt(e,t);if(n!==null){var r=je();wt(n,e,t,r)}Bu(e,t)}};op=function(){return ee};ip=function(e,t){var n=ee;try{return ee=e,t()}finally{ee=n}};ea=function(e,t,n){switch(t){case"input":if(Kl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=As(r);if(!o)throw Error(O(90));Df(r),Kl(r,o)}}}break;case"textarea":zf(e,n);break;case"select":t=n.value,t!=null&&Tr(e,!!n.multiple,t,!1)}};Hf=Iu;Qf=rr;var Uy={usingClientEntryPoint:!1,Events:[oi,xr,As,Vf,Wf,Iu]},mo={findFiberByHostInstance:Bn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},By={bundleType:mo.bundleType,version:mo.version,rendererPackageName:mo.rendererPackageName,rendererConfig:mo.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Xt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Gf(e),e===null?null:e.stateNode},findFiberByHostInstance:mo.findFiberByHostInstance||Fy,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ri=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ri.isDisabled&&Ri.supportsFiber)try{Ns=Ri.inject(By),_t=Ri}catch{}}qe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Uy;qe.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Wu(t))throw Error(O(200));return zy(e,t,null,n)};qe.createRoot=function(e,t){if(!Wu(e))throw Error(O(299));var n=!1,r="",o=Th;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=Uu(e,1,!1,null,null,n,!1,r,o),e[Ht]=t.current,$o(e.nodeType===8?e.parentNode:e),new Vu(t)};qe.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(O(188)):(e=Object.keys(e).join(","),Error(O(268,e)));return e=Gf(t),e=e===null?null:e.stateNode,e};qe.flushSync=function(e){return rr(e)};qe.hydrate=function(e,t,n){if(!Us(t))throw Error(O(200));return Bs(null,e,t,!0,n)};qe.hydrateRoot=function(e,t,n){if(!Wu(e))throw Error(O(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",s=Th;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=bh(t,null,e,1,n??null,o,!1,i,s),e[Ht]=t.current,$o(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new $s(t)};qe.render=function(e,t,n){if(!Us(t))throw Error(O(200));return Bs(null,e,t,!1,n)};qe.unmountComponentAtNode=function(e){if(!Us(e))throw Error(O(40));return e._reactRootContainer?(rr(function(){Bs(null,null,e,!1,function(){e._reactRootContainer=null,e[Ht]=null})}),!0):!1};qe.unstable_batchedUpdates=Iu;qe.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Us(n))throw Error(O(200));if(e==null||e._reactInternals===void 0)throw Error(O(38));return Bs(e,t,n,!1,r)};qe.version="18.3.1-next-f1338f8080-20240426";function Nh(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Nh)}catch(e){console.error(e)}}Nh(),Nf.exports=qe;var si=Nf.exports;const Rh=mf(si);var Oh,kd=si;Oh=kd.createRoot,kd.hydrateRoot;const Vy=1,Wy=1e6;let jl=0;function Hy(){return jl=(jl+1)%Number.MAX_SAFE_INTEGER,jl.toString()}const Ml=new Map,Pd=e=>{if(Ml.has(e))return;const t=setTimeout(()=>{Ml.delete(e),_o({type:"REMOVE_TOAST",toastId:e})},Wy);Ml.set(e,t)},Qy=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,Vy)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(n=>n.id===t.toast.id?{...n,...t.toast}:n)};case"DISMISS_TOAST":{const{toastId:n}=t;return n?Pd(n):e.toasts.forEach(r=>{Pd(r.id)}),{...e,toasts:e.toasts.map(r=>r.id===n||n===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(n=>n.id!==t.toastId)}}},Ki=[];let Yi={toasts:[]};function _o(e){Yi=Qy(Yi,e),Ki.forEach(t=>{t(Yi)})}function Ky({...e}){const t=Hy(),n=o=>_o({type:"UPDATE_TOAST",toast:{...o,id:t}}),r=()=>_o({type:"DISMISS_TOAST",toastId:t});return _o({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:o=>{o||r()}}}),{id:t,dismiss:r,update:n}}function Yy(){const[e,t]=x.useState(Yi);return x.useEffect(()=>(Ki.push(t),()=>{const n=Ki.indexOf(t);n>-1&&Ki.splice(n,1)}),[e]),{...e,toast:Ky,dismiss:n=>_o({type:"DISMISS_TOAST",toastId:n})}}function ve(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function bd(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function _h(...e){return t=>{let n=!1;const r=e.map(o=>{const i=bd(o,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let o=0;o<r.length;o++){const i=r[o];typeof i=="function"?i():bd(e[o],null)}}}}function St(...e){return x.useCallback(_h(...e),e)}function Vs(e,t=[]){let n=[];function r(i,s){const l=x.createContext(s),a=n.length;n=[...n,s];const u=f=>{var m;const{scope:h,children:d,...w}=f,v=((m=h==null?void 0:h[e])==null?void 0:m[a])||l,y=x.useMemo(()=>w,Object.values(w));return E.jsx(v.Provider,{value:y,children:d})};u.displayName=i+"Provider";function c(f,h){var v;const d=((v=h==null?void 0:h[e])==null?void 0:v[a])||l,w=x.useContext(d);if(w)return w;if(s!==void 0)return s;throw new Error(`\`${f}\` must be used within \`${i}\``)}return[u,c]}const o=()=>{const i=n.map(s=>x.createContext(s));return function(l){const a=(l==null?void 0:l[e])||i;return x.useMemo(()=>({[`__scope${e}`]:{...l,[e]:a}}),[l,a])}};return o.scopeName=e,[r,Gy(o,...t)]}function Gy(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((l,{useScope:a,scopeName:u})=>{const f=a(i)[`__scope${u}`];return{...l,...f}},{});return x.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}function La(e){const t=Xy(e),n=x.forwardRef((r,o)=>{const{children:i,...s}=r,l=x.Children.toArray(i),a=l.find(Zy);if(a){const u=a.props.children,c=l.map(f=>f===a?x.Children.count(u)>1?x.Children.only(null):x.isValidElement(u)?u.props.children:null:f);return E.jsx(t,{...s,ref:o,children:x.isValidElement(u)?x.cloneElement(u,void 0,c):null})}return E.jsx(t,{...s,ref:o,children:i})});return n.displayName=`${e}.Slot`,n}function Xy(e){const t=x.forwardRef((n,r)=>{const{children:o,...i}=n;if(x.isValidElement(o)){const s=e0(o),l=Jy(i,o.props);return o.type!==x.Fragment&&(l.ref=r?_h(r,s):s),x.cloneElement(o,l)}return x.Children.count(o)>1?x.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var Ah=Symbol("radix.slottable");function qy(e){const t=({children:n})=>E.jsx(E.Fragment,{children:n});return t.displayName=`${e}.Slottable`,t.__radixId=Ah,t}function Zy(e){return x.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===Ah}function Jy(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...l)=>{const a=i(...l);return o(...l),a}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function e0(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function t0(e){const t=e+"CollectionProvider",[n,r]=Vs(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=v=>{const{scope:y,children:m}=v,p=_.useRef(null),g=_.useRef(new Map).current;return E.jsx(o,{scope:y,itemMap:g,collectionRef:p,children:m})};s.displayName=t;const l=e+"CollectionSlot",a=La(l),u=_.forwardRef((v,y)=>{const{scope:m,children:p}=v,g=i(l,m),S=St(y,g.collectionRef);return E.jsx(a,{ref:S,children:p})});u.displayName=l;const c=e+"CollectionItemSlot",f="data-radix-collection-item",h=La(c),d=_.forwardRef((v,y)=>{const{scope:m,children:p,...g}=v,S=_.useRef(null),C=St(y,S),k=i(c,m);return _.useEffect(()=>(k.itemMap.set(S,{ref:S,...g}),()=>void k.itemMap.delete(S))),E.jsx(h,{[f]:"",ref:C,children:p})});d.displayName=c;function w(v){const y=i(e+"CollectionConsumer",v);return _.useCallback(()=>{const p=y.collectionRef.current;if(!p)return[];const g=Array.from(p.querySelectorAll(`[${f}]`));return Array.from(y.itemMap.values()).sort((k,P)=>g.indexOf(k.ref.current)-g.indexOf(P.ref.current))},[y.collectionRef,y.itemMap])}return[{Provider:s,Slot:u,ItemSlot:d},w,r]}var n0=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],We=n0.reduce((e,t)=>{const n=La(`Primitive.${t}`),r=x.forwardRef((o,i)=>{const{asChild:s,...l}=o,a=s?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),E.jsx(a,{...l,ref:i})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function Lh(e,t){e&&si.flushSync(()=>e.dispatchEvent(t))}function On(e){const t=x.useRef(e);return x.useEffect(()=>{t.current=e}),x.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function r0(e,t=globalThis==null?void 0:globalThis.document){const n=On(e);x.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var o0="DismissableLayer",ja="dismissableLayer.update",i0="dismissableLayer.pointerDownOutside",s0="dismissableLayer.focusOutside",Td,jh=x.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Hu=x.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:s,onDismiss:l,...a}=e,u=x.useContext(jh),[c,f]=x.useState(null),h=(c==null?void 0:c.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,d]=x.useState({}),w=St(t,P=>f(P)),v=Array.from(u.layers),[y]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),m=v.indexOf(y),p=c?v.indexOf(c):-1,g=u.layersWithOutsidePointerEventsDisabled.size>0,S=p>=m,C=a0(P=>{const R=P.target,L=[...u.branches].some(A=>A.contains(R));!S||L||(o==null||o(P),s==null||s(P),P.defaultPrevented||l==null||l())},h),k=u0(P=>{const R=P.target;[...u.branches].some(A=>A.contains(R))||(i==null||i(P),s==null||s(P),P.defaultPrevented||l==null||l())},h);return r0(P=>{p===u.layers.size-1&&(r==null||r(P),!P.defaultPrevented&&l&&(P.preventDefault(),l()))},h),x.useEffect(()=>{if(c)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(Td=h.body.style.pointerEvents,h.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(c)),u.layers.add(c),Nd(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(h.body.style.pointerEvents=Td)}},[c,h,n,u]),x.useEffect(()=>()=>{c&&(u.layers.delete(c),u.layersWithOutsidePointerEventsDisabled.delete(c),Nd())},[c,u]),x.useEffect(()=>{const P=()=>d({});return document.addEventListener(ja,P),()=>document.removeEventListener(ja,P)},[]),E.jsx(We.div,{...a,ref:w,style:{pointerEvents:g?S?"auto":"none":void 0,...e.style},onFocusCapture:ve(e.onFocusCapture,k.onFocusCapture),onBlurCapture:ve(e.onBlurCapture,k.onBlurCapture),onPointerDownCapture:ve(e.onPointerDownCapture,C.onPointerDownCapture)})});Hu.displayName=o0;var l0="DismissableLayerBranch",Mh=x.forwardRef((e,t)=>{const n=x.useContext(jh),r=x.useRef(null),o=St(t,r);return x.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),E.jsx(We.div,{...e,ref:o})});Mh.displayName=l0;function a0(e,t=globalThis==null?void 0:globalThis.document){const n=On(e),r=x.useRef(!1),o=x.useRef(()=>{});return x.useEffect(()=>{const i=l=>{if(l.target&&!r.current){let a=function(){Dh(i0,n,u,{discrete:!0})};const u={originalEvent:l};l.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=a,t.addEventListener("click",o.current,{once:!0})):a()}else t.removeEventListener("click",o.current);r.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function u0(e,t=globalThis==null?void 0:globalThis.document){const n=On(e),r=x.useRef(!1);return x.useEffect(()=>{const o=i=>{i.target&&!r.current&&Dh(s0,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Nd(){const e=new CustomEvent(ja);document.dispatchEvent(e)}function Dh(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Lh(o,i):o.dispatchEvent(i)}var c0=Hu,d0=Mh,_n=globalThis!=null&&globalThis.document?x.useLayoutEffect:()=>{},f0="Portal",Ih=x.forwardRef((e,t)=>{var l;const{container:n,...r}=e,[o,i]=x.useState(!1);_n(()=>i(!0),[]);const s=n||o&&((l=globalThis==null?void 0:globalThis.document)==null?void 0:l.body);return s?Rh.createPortal(E.jsx(We.div,{...r,ref:t}),s):null});Ih.displayName=f0;function p0(e,t){return x.useReducer((n,r)=>t[n][r]??n,e)}var Qu=e=>{const{present:t,children:n}=e,r=h0(t),o=typeof n=="function"?n({present:r.isPresent}):x.Children.only(n),i=St(r.ref,m0(o));return typeof n=="function"||r.isPresent?x.cloneElement(o,{ref:i}):null};Qu.displayName="Presence";function h0(e){const[t,n]=x.useState(),r=x.useRef(null),o=x.useRef(e),i=x.useRef("none"),s=e?"mounted":"unmounted",[l,a]=p0(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return x.useEffect(()=>{const u=Oi(r.current);i.current=l==="mounted"?u:"none"},[l]),_n(()=>{const u=r.current,c=o.current;if(c!==e){const h=i.current,d=Oi(u);e?a("MOUNT"):d==="none"||(u==null?void 0:u.display)==="none"?a("UNMOUNT"):a(c&&h!==d?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,a]),_n(()=>{if(t){let u;const c=t.ownerDocument.defaultView??window,f=d=>{const v=Oi(r.current).includes(d.animationName);if(d.target===t&&v&&(a("ANIMATION_END"),!o.current)){const y=t.style.animationFillMode;t.style.animationFillMode="forwards",u=c.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=y)})}},h=d=>{d.target===t&&(i.current=Oi(r.current))};return t.addEventListener("animationstart",h),t.addEventListener("animationcancel",f),t.addEventListener("animationend",f),()=>{c.clearTimeout(u),t.removeEventListener("animationstart",h),t.removeEventListener("animationcancel",f),t.removeEventListener("animationend",f)}}else a("ANIMATION_END")},[t,a]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:x.useCallback(u=>{r.current=u?getComputedStyle(u):null,n(u)},[])}}function Oi(e){return(e==null?void 0:e.animationName)||"none"}function m0(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var g0=bf[" useInsertionEffect ".trim().toString()]||_n;function v0({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[o,i,s]=y0({defaultProp:t,onChange:n}),l=e!==void 0,a=l?e:o;{const c=x.useRef(e!==void 0);x.useEffect(()=>{const f=c.current;f!==l&&console.warn(`${r} is changing from ${f?"controlled":"uncontrolled"} to ${l?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),c.current=l},[l,r])}const u=x.useCallback(c=>{var f;if(l){const h=w0(c)?c(e):c;h!==e&&((f=s.current)==null||f.call(s,h))}else i(c)},[l,e,i,s]);return[a,u]}function y0({defaultProp:e,onChange:t}){const[n,r]=x.useState(e),o=x.useRef(n),i=x.useRef(t);return g0(()=>{i.current=t},[t]),x.useEffect(()=>{var s;o.current!==n&&((s=i.current)==null||s.call(i,n),o.current=n)},[n,o]),[n,r,i]}function w0(e){return typeof e=="function"}var x0=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),S0="VisuallyHidden",Ws=x.forwardRef((e,t)=>E.jsx(We.span,{...e,ref:t,style:{...x0,...e.style}}));Ws.displayName=S0;var E0=Ws,Ku="ToastProvider",[Yu,C0,k0]=t0("Toast"),[zh,iE]=Vs("Toast",[k0]),[P0,Hs]=zh(Ku),Fh=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:o="right",swipeThreshold:i=50,children:s}=e,[l,a]=x.useState(null),[u,c]=x.useState(0),f=x.useRef(!1),h=x.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${Ku}\`. Expected non-empty \`string\`.`),E.jsx(Yu.Provider,{scope:t,children:E.jsx(P0,{scope:t,label:n,duration:r,swipeDirection:o,swipeThreshold:i,toastCount:u,viewport:l,onViewportChange:a,onToastAdd:x.useCallback(()=>c(d=>d+1),[]),onToastRemove:x.useCallback(()=>c(d=>d-1),[]),isFocusedToastEscapeKeyDownRef:f,isClosePausedRef:h,children:s})})};Fh.displayName=Ku;var $h="ToastViewport",b0=["F8"],Ma="toast.viewportPause",Da="toast.viewportResume",Uh=x.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=b0,label:o="Notifications ({hotkey})",...i}=e,s=Hs($h,n),l=C0(n),a=x.useRef(null),u=x.useRef(null),c=x.useRef(null),f=x.useRef(null),h=St(t,f,s.onViewportChange),d=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),w=s.toastCount>0;x.useEffect(()=>{const y=m=>{var g;r.length!==0&&r.every(S=>m[S]||m.code===S)&&((g=f.current)==null||g.focus())};return document.addEventListener("keydown",y),()=>document.removeEventListener("keydown",y)},[r]),x.useEffect(()=>{const y=a.current,m=f.current;if(w&&y&&m){const p=()=>{if(!s.isClosePausedRef.current){const k=new CustomEvent(Ma);m.dispatchEvent(k),s.isClosePausedRef.current=!0}},g=()=>{if(s.isClosePausedRef.current){const k=new CustomEvent(Da);m.dispatchEvent(k),s.isClosePausedRef.current=!1}},S=k=>{!y.contains(k.relatedTarget)&&g()},C=()=>{y.contains(document.activeElement)||g()};return y.addEventListener("focusin",p),y.addEventListener("focusout",S),y.addEventListener("pointermove",p),y.addEventListener("pointerleave",C),window.addEventListener("blur",p),window.addEventListener("focus",g),()=>{y.removeEventListener("focusin",p),y.removeEventListener("focusout",S),y.removeEventListener("pointermove",p),y.removeEventListener("pointerleave",C),window.removeEventListener("blur",p),window.removeEventListener("focus",g)}}},[w,s.isClosePausedRef]);const v=x.useCallback(({tabbingDirection:y})=>{const p=l().map(g=>{const S=g.ref.current,C=[S,...F0(S)];return y==="forwards"?C:C.reverse()});return(y==="forwards"?p.reverse():p).flat()},[l]);return x.useEffect(()=>{const y=f.current;if(y){const m=p=>{var C,k,P;const g=p.altKey||p.ctrlKey||p.metaKey;if(p.key==="Tab"&&!g){const R=document.activeElement,L=p.shiftKey;if(p.target===y&&L){(C=u.current)==null||C.focus();return}const I=v({tabbingDirection:L?"backwards":"forwards"}),Q=I.findIndex(j=>j===R);Dl(I.slice(Q+1))?p.preventDefault():L?(k=u.current)==null||k.focus():(P=c.current)==null||P.focus()}};return y.addEventListener("keydown",m),()=>y.removeEventListener("keydown",m)}},[l,v]),E.jsxs(d0,{ref:a,role:"region","aria-label":o.replace("{hotkey}",d),tabIndex:-1,style:{pointerEvents:w?void 0:"none"},children:[w&&E.jsx(Ia,{ref:u,onFocusFromOutsideViewport:()=>{const y=v({tabbingDirection:"forwards"});Dl(y)}}),E.jsx(Yu.Slot,{scope:n,children:E.jsx(We.ol,{tabIndex:-1,...i,ref:h})}),w&&E.jsx(Ia,{ref:c,onFocusFromOutsideViewport:()=>{const y=v({tabbingDirection:"backwards"});Dl(y)}})]})});Uh.displayName=$h;var Bh="ToastFocusProxy",Ia=x.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,i=Hs(Bh,n);return E.jsx(Ws,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:s=>{var u;const l=s.relatedTarget;!((u=i.viewport)!=null&&u.contains(l))&&r()}})});Ia.displayName=Bh;var li="Toast",T0="toast.swipeStart",N0="toast.swipeMove",R0="toast.swipeCancel",O0="toast.swipeEnd",Vh=x.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:i,...s}=e,[l,a]=v0({prop:r,defaultProp:o??!0,onChange:i,caller:li});return E.jsx(Qu,{present:n||l,children:E.jsx(L0,{open:l,...s,ref:t,onClose:()=>a(!1),onPause:On(e.onPause),onResume:On(e.onResume),onSwipeStart:ve(e.onSwipeStart,u=>{u.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:ve(e.onSwipeMove,u=>{const{x:c,y:f}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","move"),u.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${c}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${f}px`)}),onSwipeCancel:ve(e.onSwipeCancel,u=>{u.currentTarget.setAttribute("data-swipe","cancel"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:ve(e.onSwipeEnd,u=>{const{x:c,y:f}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","end"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${c}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${f}px`),a(!1)})})})});Vh.displayName=li;var[_0,A0]=zh(li,{onClose(){}}),L0=x.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:o,open:i,onClose:s,onEscapeKeyDown:l,onPause:a,onResume:u,onSwipeStart:c,onSwipeMove:f,onSwipeCancel:h,onSwipeEnd:d,...w}=e,v=Hs(li,n),[y,m]=x.useState(null),p=St(t,j=>m(j)),g=x.useRef(null),S=x.useRef(null),C=o||v.duration,k=x.useRef(0),P=x.useRef(C),R=x.useRef(0),{onToastAdd:L,onToastRemove:A}=v,F=On(()=>{var G;(y==null?void 0:y.contains(document.activeElement))&&((G=v.viewport)==null||G.focus()),s()}),I=x.useCallback(j=>{!j||j===1/0||(window.clearTimeout(R.current),k.current=new Date().getTime(),R.current=window.setTimeout(F,j))},[F]);x.useEffect(()=>{const j=v.viewport;if(j){const G=()=>{I(P.current),u==null||u()},$=()=>{const V=new Date().getTime()-k.current;P.current=P.current-V,window.clearTimeout(R.current),a==null||a()};return j.addEventListener(Ma,$),j.addEventListener(Da,G),()=>{j.removeEventListener(Ma,$),j.removeEventListener(Da,G)}}},[v.viewport,C,a,u,I]),x.useEffect(()=>{i&&!v.isClosePausedRef.current&&I(C)},[i,C,v.isClosePausedRef,I]),x.useEffect(()=>(L(),()=>A()),[L,A]);const Q=x.useMemo(()=>y?Xh(y):null,[y]);return v.viewport?E.jsxs(E.Fragment,{children:[Q&&E.jsx(j0,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:Q}),E.jsx(_0,{scope:n,onClose:F,children:si.createPortal(E.jsx(Yu.ItemSlot,{scope:n,children:E.jsx(c0,{asChild:!0,onEscapeKeyDown:ve(l,()=>{v.isFocusedToastEscapeKeyDownRef.current||F(),v.isFocusedToastEscapeKeyDownRef.current=!1}),children:E.jsx(We.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":i?"open":"closed","data-swipe-direction":v.swipeDirection,...w,ref:p,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:ve(e.onKeyDown,j=>{j.key==="Escape"&&(l==null||l(j.nativeEvent),j.nativeEvent.defaultPrevented||(v.isFocusedToastEscapeKeyDownRef.current=!0,F()))}),onPointerDown:ve(e.onPointerDown,j=>{j.button===0&&(g.current={x:j.clientX,y:j.clientY})}),onPointerMove:ve(e.onPointerMove,j=>{if(!g.current)return;const G=j.clientX-g.current.x,$=j.clientY-g.current.y,V=!!S.current,T=["left","right"].includes(v.swipeDirection),N=["left","up"].includes(v.swipeDirection)?Math.min:Math.max,M=T?N(0,G):0,W=T?0:N(0,$),z=j.pointerType==="touch"?10:2,K={x:M,y:W},X={originalEvent:j,delta:K};V?(S.current=K,_i(N0,f,X,{discrete:!1})):Rd(K,v.swipeDirection,z)?(S.current=K,_i(T0,c,X,{discrete:!1}),j.target.setPointerCapture(j.pointerId)):(Math.abs(G)>z||Math.abs($)>z)&&(g.current=null)}),onPointerUp:ve(e.onPointerUp,j=>{const G=S.current,$=j.target;if($.hasPointerCapture(j.pointerId)&&$.releasePointerCapture(j.pointerId),S.current=null,g.current=null,G){const V=j.currentTarget,T={originalEvent:j,delta:G};Rd(G,v.swipeDirection,v.swipeThreshold)?_i(O0,d,T,{discrete:!0}):_i(R0,h,T,{discrete:!0}),V.addEventListener("click",N=>N.preventDefault(),{once:!0})}})})})}),v.viewport)})]}):null}),j0=e=>{const{__scopeToast:t,children:n,...r}=e,o=Hs(li,t),[i,s]=x.useState(!1),[l,a]=x.useState(!1);return I0(()=>s(!0)),x.useEffect(()=>{const u=window.setTimeout(()=>a(!0),1e3);return()=>window.clearTimeout(u)},[]),l?null:E.jsx(Ih,{asChild:!0,children:E.jsx(Ws,{...r,children:i&&E.jsxs(E.Fragment,{children:[o.label," ",n]})})})},M0="ToastTitle",Wh=x.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return E.jsx(We.div,{...r,ref:t})});Wh.displayName=M0;var D0="ToastDescription",Hh=x.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return E.jsx(We.div,{...r,ref:t})});Hh.displayName=D0;var Qh="ToastAction",Kh=x.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?E.jsx(Gh,{altText:n,asChild:!0,children:E.jsx(Gu,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${Qh}\`. Expected non-empty \`string\`.`),null)});Kh.displayName=Qh;var Yh="ToastClose",Gu=x.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,o=A0(Yh,n);return E.jsx(Gh,{asChild:!0,children:E.jsx(We.button,{type:"button",...r,ref:t,onClick:ve(e.onClick,o.onClose)})})});Gu.displayName=Yh;var Gh=x.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...o}=e;return E.jsx(We.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function Xh(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),z0(r)){const o=r.ariaHidden||r.hidden||r.style.display==="none",i=r.dataset.radixToastAnnounceExclude==="";if(!o)if(i){const s=r.dataset.radixToastAnnounceAlt;s&&t.push(s)}else t.push(...Xh(r))}}),t}function _i(e,t,n,{discrete:r}){const o=n.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Lh(o,i):o.dispatchEvent(i)}var Rd=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),i=r>o;return t==="left"||t==="right"?i&&r>n:!i&&o>n};function I0(e=()=>{}){const t=On(e);_n(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function z0(e){return e.nodeType===e.ELEMENT_NODE}function F0(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Dl(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var $0=Fh,qh=Uh,Zh=Vh,Jh=Wh,em=Hh,tm=Kh,nm=Gu;function rm(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=rm(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function om(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=rm(e))&&(r&&(r+=" "),r+=t);return r}const Od=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,_d=om,U0=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return _d(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:i}=t,s=Object.keys(o).map(u=>{const c=n==null?void 0:n[u],f=i==null?void 0:i[u];if(c===null)return null;const h=Od(c)||Od(f);return o[u][h]}),l=n&&Object.entries(n).reduce((u,c)=>{let[f,h]=c;return h===void 0||(u[f]=h),u},{}),a=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,c)=>{let{class:f,className:h,...d}=c;return Object.entries(d).every(w=>{let[v,y]=w;return Array.isArray(y)?y.includes({...i,...l}[v]):{...i,...l}[v]===y})?[...u,f,h]:u},[]);return _d(e,s,a,n==null?void 0:n.class,n==null?void 0:n.className)};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const B0=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),im=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim();/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var V0={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const W0=x.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:i,iconNode:s,...l},a)=>x.createElement("svg",{ref:a,...V0,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:im("lucide",o),...l},[...s.map(([u,c])=>x.createElement(u,c)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qs=(e,t)=>{const n=x.forwardRef(({className:r,...o},i)=>x.createElement(W0,{ref:i,iconNode:t,className:im(`lucide-${B0(e)}`,r),...o}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const za=Qs("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fa=Qs("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const H0=Qs("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Q0=Qs("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Xu="-",K0=e=>{const t=G0(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:s=>{const l=s.split(Xu);return l[0]===""&&l.length!==1&&l.shift(),sm(l,t)||Y0(s)},getConflictingClassGroupIds:(s,l)=>{const a=n[s]||[];return l&&r[s]?[...a,...r[s]]:a}}},sm=(e,t)=>{var s;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?sm(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const i=e.join(Xu);return(s=t.validators.find(({validator:l})=>l(i)))==null?void 0:s.classGroupId},Ad=/^\[(.+)\]$/,Y0=e=>{if(Ad.test(e)){const t=Ad.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},G0=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return q0(Object.entries(e.classGroups),n).forEach(([i,s])=>{$a(s,r,i,t)}),r},$a=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const i=o===""?t:Ld(t,o);i.classGroupId=n;return}if(typeof o=="function"){if(X0(o)){$a(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([i,s])=>{$a(s,Ld(t,i),n,r)})})},Ld=(e,t)=>{let n=e;return t.split(Xu).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},X0=e=>e.isThemeGetter,q0=(e,t)=>t?e.map(([n,r])=>{const o=r.map(i=>typeof i=="string"?t+i:typeof i=="object"?Object.fromEntries(Object.entries(i).map(([s,l])=>[t+s,l])):i);return[n,o]}):e,Z0=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(i,s)=>{n.set(i,s),t++,t>e&&(t=0,r=n,n=new Map)};return{get(i){let s=n.get(i);if(s!==void 0)return s;if((s=r.get(i))!==void 0)return o(i,s),s},set(i,s){n.has(i)?n.set(i,s):o(i,s)}}},lm="!",J0=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],i=t.length,s=l=>{const a=[];let u=0,c=0,f;for(let y=0;y<l.length;y++){let m=l[y];if(u===0){if(m===o&&(r||l.slice(y,y+i)===t)){a.push(l.slice(c,y)),c=y+i;continue}if(m==="/"){f=y;continue}}m==="["?u++:m==="]"&&u--}const h=a.length===0?l:l.substring(c),d=h.startsWith(lm),w=d?h.substring(1):h,v=f&&f>c?f-c:void 0;return{modifiers:a,hasImportantModifier:d,baseClassName:w,maybePostfixModifierPosition:v}};return n?l=>n({className:l,parseClassName:s}):s},ew=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},tw=e=>({cache:Z0(e.cacheSize),parseClassName:J0(e),...K0(e)}),nw=/\s+/,rw=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,i=[],s=e.trim().split(nw);let l="";for(let a=s.length-1;a>=0;a-=1){const u=s[a],{modifiers:c,hasImportantModifier:f,baseClassName:h,maybePostfixModifierPosition:d}=n(u);let w=!!d,v=r(w?h.substring(0,d):h);if(!v){if(!w){l=u+(l.length>0?" "+l:l);continue}if(v=r(h),!v){l=u+(l.length>0?" "+l:l);continue}w=!1}const y=ew(c).join(":"),m=f?y+lm:y,p=m+v;if(i.includes(p))continue;i.push(p);const g=o(v,w);for(let S=0;S<g.length;++S){const C=g[S];i.push(m+C)}l=u+(l.length>0?" "+l:l)}return l};function ow(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=am(t))&&(r&&(r+=" "),r+=n);return r}const am=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=am(e[r]))&&(n&&(n+=" "),n+=t);return n};function iw(e,...t){let n,r,o,i=s;function s(a){const u=t.reduce((c,f)=>f(c),e());return n=tw(u),r=n.cache.get,o=n.cache.set,i=l,l(a)}function l(a){const u=r(a);if(u)return u;const c=rw(a,n);return o(a,c),c}return function(){return i(ow.apply(null,arguments))}}const re=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},um=/^\[(?:([a-z-]+):)?(.+)\]$/i,sw=/^\d+\/\d+$/,lw=new Set(["px","full","screen"]),aw=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,uw=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,cw=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,dw=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,fw=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Dt=e=>Lr(e)||lw.has(e)||sw.test(e),on=e=>no(e,"length",xw),Lr=e=>!!e&&!Number.isNaN(Number(e)),Il=e=>no(e,"number",Lr),go=e=>!!e&&Number.isInteger(Number(e)),pw=e=>e.endsWith("%")&&Lr(e.slice(0,-1)),H=e=>um.test(e),sn=e=>aw.test(e),hw=new Set(["length","size","percentage"]),mw=e=>no(e,hw,cm),gw=e=>no(e,"position",cm),vw=new Set(["image","url"]),yw=e=>no(e,vw,Ew),ww=e=>no(e,"",Sw),vo=()=>!0,no=(e,t,n)=>{const r=um.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},xw=e=>uw.test(e)&&!cw.test(e),cm=()=>!1,Sw=e=>dw.test(e),Ew=e=>fw.test(e),Cw=()=>{const e=re("colors"),t=re("spacing"),n=re("blur"),r=re("brightness"),o=re("borderColor"),i=re("borderRadius"),s=re("borderSpacing"),l=re("borderWidth"),a=re("contrast"),u=re("grayscale"),c=re("hueRotate"),f=re("invert"),h=re("gap"),d=re("gradientColorStops"),w=re("gradientColorStopPositions"),v=re("inset"),y=re("margin"),m=re("opacity"),p=re("padding"),g=re("saturate"),S=re("scale"),C=re("sepia"),k=re("skew"),P=re("space"),R=re("translate"),L=()=>["auto","contain","none"],A=()=>["auto","hidden","clip","visible","scroll"],F=()=>["auto",H,t],I=()=>[H,t],Q=()=>["",Dt,on],j=()=>["auto",Lr,H],G=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],$=()=>["solid","dashed","dotted","double","none"],V=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],T=()=>["start","end","center","between","around","evenly","stretch"],N=()=>["","0",H],M=()=>["auto","avoid","all","avoid-page","page","left","right","column"],W=()=>[Lr,H];return{cacheSize:500,separator:":",theme:{colors:[vo],spacing:[Dt,on],blur:["none","",sn,H],brightness:W(),borderColor:[e],borderRadius:["none","","full",sn,H],borderSpacing:I(),borderWidth:Q(),contrast:W(),grayscale:N(),hueRotate:W(),invert:N(),gap:I(),gradientColorStops:[e],gradientColorStopPositions:[pw,on],inset:F(),margin:F(),opacity:W(),padding:I(),saturate:W(),scale:W(),sepia:N(),skew:W(),space:I(),translate:I()},classGroups:{aspect:[{aspect:["auto","square","video",H]}],container:["container"],columns:[{columns:[sn]}],"break-after":[{"break-after":M()}],"break-before":[{"break-before":M()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...G(),H]}],overflow:[{overflow:A()}],"overflow-x":[{"overflow-x":A()}],"overflow-y":[{"overflow-y":A()}],overscroll:[{overscroll:L()}],"overscroll-x":[{"overscroll-x":L()}],"overscroll-y":[{"overscroll-y":L()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[v]}],"inset-x":[{"inset-x":[v]}],"inset-y":[{"inset-y":[v]}],start:[{start:[v]}],end:[{end:[v]}],top:[{top:[v]}],right:[{right:[v]}],bottom:[{bottom:[v]}],left:[{left:[v]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",go,H]}],basis:[{basis:F()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",H]}],grow:[{grow:N()}],shrink:[{shrink:N()}],order:[{order:["first","last","none",go,H]}],"grid-cols":[{"grid-cols":[vo]}],"col-start-end":[{col:["auto",{span:["full",go,H]},H]}],"col-start":[{"col-start":j()}],"col-end":[{"col-end":j()}],"grid-rows":[{"grid-rows":[vo]}],"row-start-end":[{row:["auto",{span:[go,H]},H]}],"row-start":[{"row-start":j()}],"row-end":[{"row-end":j()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",H]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",H]}],gap:[{gap:[h]}],"gap-x":[{"gap-x":[h]}],"gap-y":[{"gap-y":[h]}],"justify-content":[{justify:["normal",...T()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...T(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...T(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[p]}],px:[{px:[p]}],py:[{py:[p]}],ps:[{ps:[p]}],pe:[{pe:[p]}],pt:[{pt:[p]}],pr:[{pr:[p]}],pb:[{pb:[p]}],pl:[{pl:[p]}],m:[{m:[y]}],mx:[{mx:[y]}],my:[{my:[y]}],ms:[{ms:[y]}],me:[{me:[y]}],mt:[{mt:[y]}],mr:[{mr:[y]}],mb:[{mb:[y]}],ml:[{ml:[y]}],"space-x":[{"space-x":[P]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[P]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",H,t]}],"min-w":[{"min-w":[H,t,"min","max","fit"]}],"max-w":[{"max-w":[H,t,"none","full","min","max","fit","prose",{screen:[sn]},sn]}],h:[{h:[H,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[H,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[H,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[H,t,"auto","min","max","fit"]}],"font-size":[{text:["base",sn,on]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Il]}],"font-family":[{font:[vo]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",H]}],"line-clamp":[{"line-clamp":["none",Lr,Il]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Dt,H]}],"list-image":[{"list-image":["none",H]}],"list-style-type":[{list:["none","disc","decimal",H]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[m]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[m]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...$(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Dt,on]}],"underline-offset":[{"underline-offset":["auto",Dt,H]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:I()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",H]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",H]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[m]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...G(),gw]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",mw]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},yw]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[w]}],"gradient-via-pos":[{via:[w]}],"gradient-to-pos":[{to:[w]}],"gradient-from":[{from:[d]}],"gradient-via":[{via:[d]}],"gradient-to":[{to:[d]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[l]}],"border-w-x":[{"border-x":[l]}],"border-w-y":[{"border-y":[l]}],"border-w-s":[{"border-s":[l]}],"border-w-e":[{"border-e":[l]}],"border-w-t":[{"border-t":[l]}],"border-w-r":[{"border-r":[l]}],"border-w-b":[{"border-b":[l]}],"border-w-l":[{"border-l":[l]}],"border-opacity":[{"border-opacity":[m]}],"border-style":[{border:[...$(),"hidden"]}],"divide-x":[{"divide-x":[l]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[l]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[m]}],"divide-style":[{divide:$()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...$()]}],"outline-offset":[{"outline-offset":[Dt,H]}],"outline-w":[{outline:[Dt,on]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:Q()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[m]}],"ring-offset-w":[{"ring-offset":[Dt,on]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",sn,ww]}],"shadow-color":[{shadow:[vo]}],opacity:[{opacity:[m]}],"mix-blend":[{"mix-blend":[...V(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":V()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[a]}],"drop-shadow":[{"drop-shadow":["","none",sn,H]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[f]}],saturate:[{saturate:[g]}],sepia:[{sepia:[C]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[a]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[f]}],"backdrop-opacity":[{"backdrop-opacity":[m]}],"backdrop-saturate":[{"backdrop-saturate":[g]}],"backdrop-sepia":[{"backdrop-sepia":[C]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",H]}],duration:[{duration:W()}],ease:[{ease:["linear","in","out","in-out",H]}],delay:[{delay:W()}],animate:[{animate:["none","spin","ping","pulse","bounce",H]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[S]}],"scale-x":[{"scale-x":[S]}],"scale-y":[{"scale-y":[S]}],rotate:[{rotate:[go,H]}],"translate-x":[{"translate-x":[R]}],"translate-y":[{"translate-y":[R]}],"skew-x":[{"skew-x":[k]}],"skew-y":[{"skew-y":[k]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",H]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",H]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":I()}],"scroll-mx":[{"scroll-mx":I()}],"scroll-my":[{"scroll-my":I()}],"scroll-ms":[{"scroll-ms":I()}],"scroll-me":[{"scroll-me":I()}],"scroll-mt":[{"scroll-mt":I()}],"scroll-mr":[{"scroll-mr":I()}],"scroll-mb":[{"scroll-mb":I()}],"scroll-ml":[{"scroll-ml":I()}],"scroll-p":[{"scroll-p":I()}],"scroll-px":[{"scroll-px":I()}],"scroll-py":[{"scroll-py":I()}],"scroll-ps":[{"scroll-ps":I()}],"scroll-pe":[{"scroll-pe":I()}],"scroll-pt":[{"scroll-pt":I()}],"scroll-pr":[{"scroll-pr":I()}],"scroll-pb":[{"scroll-pb":I()}],"scroll-pl":[{"scroll-pl":I()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",H]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Dt,on,Il]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},kw=iw(Cw);function lr(...e){return kw(om(e))}const Pw=$0,dm=x.forwardRef(({className:e,...t},n)=>E.jsx(qh,{ref:n,className:lr("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));dm.displayName=qh.displayName;const bw=U0("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),fm=x.forwardRef(({className:e,variant:t,...n},r)=>E.jsx(Zh,{ref:r,className:lr(bw({variant:t}),e),...n}));fm.displayName=Zh.displayName;const Tw=x.forwardRef(({className:e,...t},n)=>E.jsx(tm,{ref:n,className:lr("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}));Tw.displayName=tm.displayName;const pm=x.forwardRef(({className:e,...t},n)=>E.jsx(nm,{ref:n,className:lr("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:E.jsx(Q0,{className:"h-4 w-4"})}));pm.displayName=nm.displayName;const hm=x.forwardRef(({className:e,...t},n)=>E.jsx(Jh,{ref:n,className:lr("text-sm font-semibold",e),...t}));hm.displayName=Jh.displayName;const mm=x.forwardRef(({className:e,...t},n)=>E.jsx(em,{ref:n,className:lr("text-sm opacity-90",e),...t}));mm.displayName=em.displayName;function Nw(){const{toasts:e}=Yy();return E.jsxs(Pw,{children:[e.map(function({id:t,title:n,description:r,action:o,...i}){return E.jsxs(fm,{...i,children:[E.jsxs("div",{className:"grid gap-1",children:[n&&E.jsx(hm,{children:n}),r&&E.jsx(mm,{children:r})]}),o,E.jsx(pm,{})]},t)}),E.jsx(dm,{})]})}var jd=["light","dark"],Rw="(prefers-color-scheme: dark)",Ow=x.createContext(void 0),_w={setTheme:e=>{},themes:[]},Aw=()=>{var e;return(e=x.useContext(Ow))!=null?e:_w};x.memo(({forcedTheme:e,storageKey:t,attribute:n,enableSystem:r,enableColorScheme:o,defaultTheme:i,value:s,attrs:l,nonce:a})=>{let u=i==="system",c=n==="class"?`var d=document.documentElement,c=d.classList;${`c.remove(${l.map(w=>`'${w}'`).join(",")})`};`:`var d=document.documentElement,n='${n}',s='setAttribute';`,f=o?jd.includes(i)&&i?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${i}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",h=(w,v=!1,y=!0)=>{let m=s?s[w]:w,p=v?w+"|| ''":`'${m}'`,g="";return o&&y&&!v&&jd.includes(w)&&(g+=`d.style.colorScheme = '${w}';`),n==="class"?v||m?g+=`c.add(${p})`:g+="null":m&&(g+=`d[s](n,${p})`),g},d=e?`!function(){${c}${h(e)}}()`:r?`!function(){try{${c}var e=localStorage.getItem('${t}');if('system'===e||(!e&&${u})){var t='${Rw}',m=window.matchMedia(t);if(m.media!==t||m.matches){${h("dark")}}else{${h("light")}}}else if(e){${s?`var x=${JSON.stringify(s)};`:""}${h(s?"x[e]":"e",!0)}}${u?"":"else{"+h(i,!1,!1)+"}"}${f}}catch(e){}}()`:`!function(){try{${c}var e=localStorage.getItem('${t}');if(e){${s?`var x=${JSON.stringify(s)};`:""}${h(s?"x[e]":"e",!0)}}else{${h(i,!1,!1)};}${f}}catch(t){}}();`;return x.createElement("script",{nonce:a,dangerouslySetInnerHTML:{__html:d}})});var Lw=e=>{switch(e){case"success":return Dw;case"info":return zw;case"warning":return Iw;case"error":return Fw;default:return null}},jw=Array(12).fill(0),Mw=({visible:e,className:t})=>_.createElement("div",{className:["sonner-loading-wrapper",t].filter(Boolean).join(" "),"data-visible":e},_.createElement("div",{className:"sonner-spinner"},jw.map((n,r)=>_.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${r}`})))),Dw=_.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},_.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),Iw=_.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},_.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),zw=_.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},_.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),Fw=_.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},_.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),$w=_.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},_.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),_.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),Uw=()=>{let[e,t]=_.useState(document.hidden);return _.useEffect(()=>{let n=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",n),()=>window.removeEventListener("visibilitychange",n)},[]),e},Ua=1,Bw=class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:n,...r}=e,o=typeof(e==null?void 0:e.id)=="number"||((t=e.id)==null?void 0:t.length)>0?e.id:Ua++,i=this.toasts.find(l=>l.id===o),s=e.dismissible===void 0?!0:e.dismissible;return this.dismissedToasts.has(o)&&this.dismissedToasts.delete(o),i?this.toasts=this.toasts.map(l=>l.id===o?(this.publish({...l,...e,id:o,title:n}),{...l,...e,id:o,dismissible:s,title:n}):l):this.addToast({title:n,...r,dismissible:s,id:o}),o},this.dismiss=e=>(this.dismissedToasts.add(e),e||this.toasts.forEach(t=>{this.subscribers.forEach(n=>n({id:t.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{if(!t)return;let n;t.loading!==void 0&&(n=this.create({...t,promise:e,type:"loading",message:t.loading,description:typeof t.description!="function"?t.description:void 0}));let r=e instanceof Promise?e:e(),o=n!==void 0,i,s=r.then(async a=>{if(i=["resolve",a],_.isValidElement(a))o=!1,this.create({id:n,type:"default",message:a});else if(Ww(a)&&!a.ok){o=!1;let u=typeof t.error=="function"?await t.error(`HTTP error! status: ${a.status}`):t.error,c=typeof t.description=="function"?await t.description(`HTTP error! status: ${a.status}`):t.description;this.create({id:n,type:"error",message:u,description:c})}else if(t.success!==void 0){o=!1;let u=typeof t.success=="function"?await t.success(a):t.success,c=typeof t.description=="function"?await t.description(a):t.description;this.create({id:n,type:"success",message:u,description:c})}}).catch(async a=>{if(i=["reject",a],t.error!==void 0){o=!1;let u=typeof t.error=="function"?await t.error(a):t.error,c=typeof t.description=="function"?await t.description(a):t.description;this.create({id:n,type:"error",message:u,description:c})}}).finally(()=>{var a;o&&(this.dismiss(n),n=void 0),(a=t.finally)==null||a.call(t)}),l=()=>new Promise((a,u)=>s.then(()=>i[0]==="reject"?u(i[1]):a(i[1])).catch(u));return typeof n!="string"&&typeof n!="number"?{unwrap:l}:Object.assign(n,{unwrap:l})},this.custom=(e,t)=>{let n=(t==null?void 0:t.id)||Ua++;return this.create({jsx:e(n),id:n,...t}),n},this.getActiveToasts=()=>this.toasts.filter(e=>!this.dismissedToasts.has(e.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}},Ie=new Bw,Vw=(e,t)=>{let n=(t==null?void 0:t.id)||Ua++;return Ie.addToast({title:e,...t,id:n}),n},Ww=e=>e&&typeof e=="object"&&"ok"in e&&typeof e.ok=="boolean"&&"status"in e&&typeof e.status=="number",Hw=Vw,Qw=()=>Ie.toasts,Kw=()=>Ie.getActiveToasts();Object.assign(Hw,{success:Ie.success,info:Ie.info,warning:Ie.warning,error:Ie.error,custom:Ie.custom,message:Ie.message,promise:Ie.promise,dismiss:Ie.dismiss,loading:Ie.loading},{getHistory:Qw,getToasts:Kw});function Yw(e,{insertAt:t}={}){if(typeof document>"u")return;let n=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",t==="top"&&n.firstChild?n.insertBefore(r,n.firstChild):n.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}Yw(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted="true"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted="true"]){transform:none}}:where([data-sonner-toaster][data-x-position="right"]){right:var(--offset-right)}:where([data-sonner-toaster][data-x-position="left"]){left:var(--offset-left)}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:var(--offset-top)}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:var(--offset-bottom)}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:-50%;right:-50%;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y, 0px)) translate(var(--swipe-amount-x, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-bg-hover: hsl(0, 0%, 12%);--normal-border: hsl(0, 0%, 20%);--normal-border-hover: hsl(0, 0%, 25%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);function Ai(e){return e.label!==void 0}var Gw=3,Xw="32px",qw="16px",Md=4e3,Zw=356,Jw=14,ex=20,tx=200;function dt(...e){return e.filter(Boolean).join(" ")}function nx(e){let[t,n]=e.split("-"),r=[];return t&&r.push(t),n&&r.push(n),r}var rx=e=>{var t,n,r,o,i,s,l,a,u,c,f;let{invert:h,toast:d,unstyled:w,interacting:v,setHeights:y,visibleToasts:m,heights:p,index:g,toasts:S,expanded:C,removeToast:k,defaultRichColors:P,closeButton:R,style:L,cancelButtonStyle:A,actionButtonStyle:F,className:I="",descriptionClassName:Q="",duration:j,position:G,gap:$,loadingIcon:V,expandByDefault:T,classNames:N,icons:M,closeButtonAriaLabel:W="Close toast",pauseWhenPageIsHidden:z}=e,[K,X]=_.useState(null),[he,ke]=_.useState(null),[J,ar]=_.useState(!1),[qt,In]=_.useState(!1),[Zt,ur]=_.useState(!1),[Jt,ui]=_.useState(!1),[rl,ci]=_.useState(!1),[ol,io]=_.useState(0),[cr,uc]=_.useState(0),so=_.useRef(d.duration||j||Md),cc=_.useRef(null),zn=_.useRef(null),ag=g===0,ug=g+1<=m,Je=d.type,dr=d.dismissible!==!1,cg=d.className||"",dg=d.descriptionClassName||"",di=_.useMemo(()=>p.findIndex(U=>U.toastId===d.id)||0,[p,d.id]),fg=_.useMemo(()=>{var U;return(U=d.closeButton)!=null?U:R},[d.closeButton,R]),dc=_.useMemo(()=>d.duration||j||Md,[d.duration,j]),il=_.useRef(0),fr=_.useRef(0),fc=_.useRef(0),pr=_.useRef(null),[pg,hg]=G.split("-"),pc=_.useMemo(()=>p.reduce((U,te,se)=>se>=di?U:U+te.height,0),[p,di]),hc=Uw(),mg=d.invert||h,sl=Je==="loading";fr.current=_.useMemo(()=>di*$+pc,[di,pc]),_.useEffect(()=>{so.current=dc},[dc]),_.useEffect(()=>{ar(!0)},[]),_.useEffect(()=>{let U=zn.current;if(U){let te=U.getBoundingClientRect().height;return uc(te),y(se=>[{toastId:d.id,height:te,position:d.position},...se]),()=>y(se=>se.filter(lt=>lt.toastId!==d.id))}},[y,d.id]),_.useLayoutEffect(()=>{if(!J)return;let U=zn.current,te=U.style.height;U.style.height="auto";let se=U.getBoundingClientRect().height;U.style.height=te,uc(se),y(lt=>lt.find(at=>at.toastId===d.id)?lt.map(at=>at.toastId===d.id?{...at,height:se}:at):[{toastId:d.id,height:se,position:d.position},...lt])},[J,d.title,d.description,y,d.id]);let en=_.useCallback(()=>{In(!0),io(fr.current),y(U=>U.filter(te=>te.toastId!==d.id)),setTimeout(()=>{k(d)},tx)},[d,k,y,fr]);_.useEffect(()=>{if(d.promise&&Je==="loading"||d.duration===1/0||d.type==="loading")return;let U;return C||v||z&&hc?(()=>{if(fc.current<il.current){let te=new Date().getTime()-il.current;so.current=so.current-te}fc.current=new Date().getTime()})():so.current!==1/0&&(il.current=new Date().getTime(),U=setTimeout(()=>{var te;(te=d.onAutoClose)==null||te.call(d,d),en()},so.current)),()=>clearTimeout(U)},[C,v,d,Je,z,hc,en]),_.useEffect(()=>{d.delete&&en()},[en,d.delete]);function gg(){var U,te,se;return M!=null&&M.loading?_.createElement("div",{className:dt(N==null?void 0:N.loader,(U=d==null?void 0:d.classNames)==null?void 0:U.loader,"sonner-loader"),"data-visible":Je==="loading"},M.loading):V?_.createElement("div",{className:dt(N==null?void 0:N.loader,(te=d==null?void 0:d.classNames)==null?void 0:te.loader,"sonner-loader"),"data-visible":Je==="loading"},V):_.createElement(Mw,{className:dt(N==null?void 0:N.loader,(se=d==null?void 0:d.classNames)==null?void 0:se.loader),visible:Je==="loading"})}return _.createElement("li",{tabIndex:0,ref:zn,className:dt(I,cg,N==null?void 0:N.toast,(t=d==null?void 0:d.classNames)==null?void 0:t.toast,N==null?void 0:N.default,N==null?void 0:N[Je],(n=d==null?void 0:d.classNames)==null?void 0:n[Je]),"data-sonner-toast":"","data-rich-colors":(r=d.richColors)!=null?r:P,"data-styled":!(d.jsx||d.unstyled||w),"data-mounted":J,"data-promise":!!d.promise,"data-swiped":rl,"data-removed":qt,"data-visible":ug,"data-y-position":pg,"data-x-position":hg,"data-index":g,"data-front":ag,"data-swiping":Zt,"data-dismissible":dr,"data-type":Je,"data-invert":mg,"data-swipe-out":Jt,"data-swipe-direction":he,"data-expanded":!!(C||T&&J),style:{"--index":g,"--toasts-before":g,"--z-index":S.length-g,"--offset":`${qt?ol:fr.current}px`,"--initial-height":T?"auto":`${cr}px`,...L,...d.style},onDragEnd:()=>{ur(!1),X(null),pr.current=null},onPointerDown:U=>{sl||!dr||(cc.current=new Date,io(fr.current),U.target.setPointerCapture(U.pointerId),U.target.tagName!=="BUTTON"&&(ur(!0),pr.current={x:U.clientX,y:U.clientY}))},onPointerUp:()=>{var U,te,se,lt;if(Jt||!dr)return;pr.current=null;let at=Number(((U=zn.current)==null?void 0:U.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),tn=Number(((te=zn.current)==null?void 0:te.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),Fn=new Date().getTime()-((se=cc.current)==null?void 0:se.getTime()),ut=K==="x"?at:tn,nn=Math.abs(ut)/Fn;if(Math.abs(ut)>=ex||nn>.11){io(fr.current),(lt=d.onDismiss)==null||lt.call(d,d),ke(K==="x"?at>0?"right":"left":tn>0?"down":"up"),en(),ui(!0),ci(!1);return}ur(!1),X(null)},onPointerMove:U=>{var te,se,lt,at;if(!pr.current||!dr||((te=window.getSelection())==null?void 0:te.toString().length)>0)return;let tn=U.clientY-pr.current.y,Fn=U.clientX-pr.current.x,ut=(se=e.swipeDirections)!=null?se:nx(G);!K&&(Math.abs(Fn)>1||Math.abs(tn)>1)&&X(Math.abs(Fn)>Math.abs(tn)?"x":"y");let nn={x:0,y:0};K==="y"?(ut.includes("top")||ut.includes("bottom"))&&(ut.includes("top")&&tn<0||ut.includes("bottom")&&tn>0)&&(nn.y=tn):K==="x"&&(ut.includes("left")||ut.includes("right"))&&(ut.includes("left")&&Fn<0||ut.includes("right")&&Fn>0)&&(nn.x=Fn),(Math.abs(nn.x)>0||Math.abs(nn.y)>0)&&ci(!0),(lt=zn.current)==null||lt.style.setProperty("--swipe-amount-x",`${nn.x}px`),(at=zn.current)==null||at.style.setProperty("--swipe-amount-y",`${nn.y}px`)}},fg&&!d.jsx?_.createElement("button",{"aria-label":W,"data-disabled":sl,"data-close-button":!0,onClick:sl||!dr?()=>{}:()=>{var U;en(),(U=d.onDismiss)==null||U.call(d,d)},className:dt(N==null?void 0:N.closeButton,(o=d==null?void 0:d.classNames)==null?void 0:o.closeButton)},(i=M==null?void 0:M.close)!=null?i:$w):null,d.jsx||x.isValidElement(d.title)?d.jsx?d.jsx:typeof d.title=="function"?d.title():d.title:_.createElement(_.Fragment,null,Je||d.icon||d.promise?_.createElement("div",{"data-icon":"",className:dt(N==null?void 0:N.icon,(s=d==null?void 0:d.classNames)==null?void 0:s.icon)},d.promise||d.type==="loading"&&!d.icon?d.icon||gg():null,d.type!=="loading"?d.icon||(M==null?void 0:M[Je])||Lw(Je):null):null,_.createElement("div",{"data-content":"",className:dt(N==null?void 0:N.content,(l=d==null?void 0:d.classNames)==null?void 0:l.content)},_.createElement("div",{"data-title":"",className:dt(N==null?void 0:N.title,(a=d==null?void 0:d.classNames)==null?void 0:a.title)},typeof d.title=="function"?d.title():d.title),d.description?_.createElement("div",{"data-description":"",className:dt(Q,dg,N==null?void 0:N.description,(u=d==null?void 0:d.classNames)==null?void 0:u.description)},typeof d.description=="function"?d.description():d.description):null),x.isValidElement(d.cancel)?d.cancel:d.cancel&&Ai(d.cancel)?_.createElement("button",{"data-button":!0,"data-cancel":!0,style:d.cancelButtonStyle||A,onClick:U=>{var te,se;Ai(d.cancel)&&dr&&((se=(te=d.cancel).onClick)==null||se.call(te,U),en())},className:dt(N==null?void 0:N.cancelButton,(c=d==null?void 0:d.classNames)==null?void 0:c.cancelButton)},d.cancel.label):null,x.isValidElement(d.action)?d.action:d.action&&Ai(d.action)?_.createElement("button",{"data-button":!0,"data-action":!0,style:d.actionButtonStyle||F,onClick:U=>{var te,se;Ai(d.action)&&((se=(te=d.action).onClick)==null||se.call(te,U),!U.defaultPrevented&&en())},className:dt(N==null?void 0:N.actionButton,(f=d==null?void 0:d.classNames)==null?void 0:f.actionButton)},d.action.label):null))};function Dd(){if(typeof window>"u"||typeof document>"u")return"ltr";let e=document.documentElement.getAttribute("dir");return e==="auto"||!e?window.getComputedStyle(document.documentElement).direction:e}function ox(e,t){let n={};return[e,t].forEach((r,o)=>{let i=o===1,s=i?"--mobile-offset":"--offset",l=i?qw:Xw;function a(u){["top","right","bottom","left"].forEach(c=>{n[`${s}-${c}`]=typeof u=="number"?`${u}px`:u})}typeof r=="number"||typeof r=="string"?a(r):typeof r=="object"?["top","right","bottom","left"].forEach(u=>{r[u]===void 0?n[`${s}-${u}`]=l:n[`${s}-${u}`]=typeof r[u]=="number"?`${r[u]}px`:r[u]}):a(l)}),n}var ix=x.forwardRef(function(e,t){let{invert:n,position:r="bottom-right",hotkey:o=["altKey","KeyT"],expand:i,closeButton:s,className:l,offset:a,mobileOffset:u,theme:c="light",richColors:f,duration:h,style:d,visibleToasts:w=Gw,toastOptions:v,dir:y=Dd(),gap:m=Jw,loadingIcon:p,icons:g,containerAriaLabel:S="Notifications",pauseWhenPageIsHidden:C}=e,[k,P]=_.useState([]),R=_.useMemo(()=>Array.from(new Set([r].concat(k.filter(z=>z.position).map(z=>z.position)))),[k,r]),[L,A]=_.useState([]),[F,I]=_.useState(!1),[Q,j]=_.useState(!1),[G,$]=_.useState(c!=="system"?c:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),V=_.useRef(null),T=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),N=_.useRef(null),M=_.useRef(!1),W=_.useCallback(z=>{P(K=>{var X;return(X=K.find(he=>he.id===z.id))!=null&&X.delete||Ie.dismiss(z.id),K.filter(({id:he})=>he!==z.id)})},[]);return _.useEffect(()=>Ie.subscribe(z=>{if(z.dismiss){P(K=>K.map(X=>X.id===z.id?{...X,delete:!0}:X));return}setTimeout(()=>{Rh.flushSync(()=>{P(K=>{let X=K.findIndex(he=>he.id===z.id);return X!==-1?[...K.slice(0,X),{...K[X],...z},...K.slice(X+1)]:[z,...K]})})})}),[]),_.useEffect(()=>{if(c!=="system"){$(c);return}if(c==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?$("dark"):$("light")),typeof window>"u")return;let z=window.matchMedia("(prefers-color-scheme: dark)");try{z.addEventListener("change",({matches:K})=>{$(K?"dark":"light")})}catch{z.addListener(({matches:X})=>{try{$(X?"dark":"light")}catch(he){console.error(he)}})}},[c]),_.useEffect(()=>{k.length<=1&&I(!1)},[k]),_.useEffect(()=>{let z=K=>{var X,he;o.every(ke=>K[ke]||K.code===ke)&&(I(!0),(X=V.current)==null||X.focus()),K.code==="Escape"&&(document.activeElement===V.current||(he=V.current)!=null&&he.contains(document.activeElement))&&I(!1)};return document.addEventListener("keydown",z),()=>document.removeEventListener("keydown",z)},[o]),_.useEffect(()=>{if(V.current)return()=>{N.current&&(N.current.focus({preventScroll:!0}),N.current=null,M.current=!1)}},[V.current]),_.createElement("section",{ref:t,"aria-label":`${S} ${T}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},R.map((z,K)=>{var X;let[he,ke]=z.split("-");return k.length?_.createElement("ol",{key:z,dir:y==="auto"?Dd():y,tabIndex:-1,ref:V,className:l,"data-sonner-toaster":!0,"data-theme":G,"data-y-position":he,"data-lifted":F&&k.length>1&&!i,"data-x-position":ke,style:{"--front-toast-height":`${((X=L[0])==null?void 0:X.height)||0}px`,"--width":`${Zw}px`,"--gap":`${m}px`,...d,...ox(a,u)},onBlur:J=>{M.current&&!J.currentTarget.contains(J.relatedTarget)&&(M.current=!1,N.current&&(N.current.focus({preventScroll:!0}),N.current=null))},onFocus:J=>{J.target instanceof HTMLElement&&J.target.dataset.dismissible==="false"||M.current||(M.current=!0,N.current=J.relatedTarget)},onMouseEnter:()=>I(!0),onMouseMove:()=>I(!0),onMouseLeave:()=>{Q||I(!1)},onDragEnd:()=>I(!1),onPointerDown:J=>{J.target instanceof HTMLElement&&J.target.dataset.dismissible==="false"||j(!0)},onPointerUp:()=>j(!1)},k.filter(J=>!J.position&&K===0||J.position===z).map((J,ar)=>{var qt,In;return _.createElement(rx,{key:J.id,icons:g,index:ar,toast:J,defaultRichColors:f,duration:(qt=v==null?void 0:v.duration)!=null?qt:h,className:v==null?void 0:v.className,descriptionClassName:v==null?void 0:v.descriptionClassName,invert:n,visibleToasts:w,closeButton:(In=v==null?void 0:v.closeButton)!=null?In:s,interacting:Q,position:z,style:v==null?void 0:v.style,unstyled:v==null?void 0:v.unstyled,classNames:v==null?void 0:v.classNames,cancelButtonStyle:v==null?void 0:v.cancelButtonStyle,actionButtonStyle:v==null?void 0:v.actionButtonStyle,removeToast:W,toasts:k.filter(Zt=>Zt.position==J.position),heights:L.filter(Zt=>Zt.position==J.position),setHeights:A,expandByDefault:i,gap:m,loadingIcon:p,expanded:F,pauseWhenPageIsHidden:C,swipeDirections:e.swipeDirections})})):null}))});const sx=({...e})=>{const{theme:t="system"}=Aw();return E.jsx(ix,{theme:t,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})},lx=["top","right","bottom","left"],An=Math.min,Qe=Math.max,xs=Math.round,Li=Math.floor,Lt=e=>({x:e,y:e}),ax={left:"right",right:"left",bottom:"top",top:"bottom"},ux={start:"end",end:"start"};function Ba(e,t,n){return Qe(e,An(t,n))}function Yt(e,t){return typeof e=="function"?e(t):e}function Gt(e){return e.split("-")[0]}function ro(e){return e.split("-")[1]}function qu(e){return e==="x"?"y":"x"}function Zu(e){return e==="y"?"height":"width"}function Ot(e){return["top","bottom"].includes(Gt(e))?"y":"x"}function Ju(e){return qu(Ot(e))}function cx(e,t,n){n===void 0&&(n=!1);const r=ro(e),o=Ju(e),i=Zu(o);let s=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(s=Ss(s)),[s,Ss(s)]}function dx(e){const t=Ss(e);return[Va(e),t,Va(t)]}function Va(e){return e.replace(/start|end/g,t=>ux[t])}function fx(e,t,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],s=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?i:s;default:return[]}}function px(e,t,n,r){const o=ro(e);let i=fx(Gt(e),n==="start",r);return o&&(i=i.map(s=>s+"-"+o),t&&(i=i.concat(i.map(Va)))),i}function Ss(e){return e.replace(/left|right|bottom|top/g,t=>ax[t])}function hx(e){return{top:0,right:0,bottom:0,left:0,...e}}function gm(e){return typeof e!="number"?hx(e):{top:e,right:e,bottom:e,left:e}}function Es(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function Id(e,t,n){let{reference:r,floating:o}=e;const i=Ot(t),s=Ju(t),l=Zu(s),a=Gt(t),u=i==="y",c=r.x+r.width/2-o.width/2,f=r.y+r.height/2-o.height/2,h=r[l]/2-o[l]/2;let d;switch(a){case"top":d={x:c,y:r.y-o.height};break;case"bottom":d={x:c,y:r.y+r.height};break;case"right":d={x:r.x+r.width,y:f};break;case"left":d={x:r.x-o.width,y:f};break;default:d={x:r.x,y:r.y}}switch(ro(t)){case"start":d[s]-=h*(n&&u?-1:1);break;case"end":d[s]+=h*(n&&u?-1:1);break}return d}const mx=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:s}=n,l=i.filter(Boolean),a=await(s.isRTL==null?void 0:s.isRTL(t));let u=await s.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:f}=Id(u,r,a),h=r,d={},w=0;for(let v=0;v<l.length;v++){const{name:y,fn:m}=l[v],{x:p,y:g,data:S,reset:C}=await m({x:c,y:f,initialPlacement:r,placement:h,strategy:o,middlewareData:d,rects:u,platform:s,elements:{reference:e,floating:t}});c=p??c,f=g??f,d={...d,[y]:{...d[y],...S}},C&&w<=50&&(w++,typeof C=="object"&&(C.placement&&(h=C.placement),C.rects&&(u=C.rects===!0?await s.getElementRects({reference:e,floating:t,strategy:o}):C.rects),{x:c,y:f}=Id(u,h,a)),v=-1)}return{x:c,y:f,placement:h,strategy:o,middlewareData:d}};async function Go(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:i,rects:s,elements:l,strategy:a}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:f="floating",altBoundary:h=!1,padding:d=0}=Yt(t,e),w=gm(d),y=l[h?f==="floating"?"reference":"floating":f],m=Es(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(y)))==null||n?y:y.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(l.floating)),boundary:u,rootBoundary:c,strategy:a})),p=f==="floating"?{x:r,y:o,width:s.floating.width,height:s.floating.height}:s.reference,g=await(i.getOffsetParent==null?void 0:i.getOffsetParent(l.floating)),S=await(i.isElement==null?void 0:i.isElement(g))?await(i.getScale==null?void 0:i.getScale(g))||{x:1,y:1}:{x:1,y:1},C=Es(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:p,offsetParent:g,strategy:a}):p);return{top:(m.top-C.top+w.top)/S.y,bottom:(C.bottom-m.bottom+w.bottom)/S.y,left:(m.left-C.left+w.left)/S.x,right:(C.right-m.right+w.right)/S.x}}const gx=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:s,elements:l,middlewareData:a}=t,{element:u,padding:c=0}=Yt(e,t)||{};if(u==null)return{};const f=gm(c),h={x:n,y:r},d=Ju(o),w=Zu(d),v=await s.getDimensions(u),y=d==="y",m=y?"top":"left",p=y?"bottom":"right",g=y?"clientHeight":"clientWidth",S=i.reference[w]+i.reference[d]-h[d]-i.floating[w],C=h[d]-i.reference[d],k=await(s.getOffsetParent==null?void 0:s.getOffsetParent(u));let P=k?k[g]:0;(!P||!await(s.isElement==null?void 0:s.isElement(k)))&&(P=l.floating[g]||i.floating[w]);const R=S/2-C/2,L=P/2-v[w]/2-1,A=An(f[m],L),F=An(f[p],L),I=A,Q=P-v[w]-F,j=P/2-v[w]/2+R,G=Ba(I,j,Q),$=!a.arrow&&ro(o)!=null&&j!==G&&i.reference[w]/2-(j<I?A:F)-v[w]/2<0,V=$?j<I?j-I:j-Q:0;return{[d]:h[d]+V,data:{[d]:G,centerOffset:j-G-V,...$&&{alignmentOffset:V}},reset:$}}}),vx=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:s,initialPlacement:l,platform:a,elements:u}=t,{mainAxis:c=!0,crossAxis:f=!0,fallbackPlacements:h,fallbackStrategy:d="bestFit",fallbackAxisSideDirection:w="none",flipAlignment:v=!0,...y}=Yt(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const m=Gt(o),p=Ot(l),g=Gt(l)===l,S=await(a.isRTL==null?void 0:a.isRTL(u.floating)),C=h||(g||!v?[Ss(l)]:dx(l)),k=w!=="none";!h&&k&&C.push(...px(l,v,w,S));const P=[l,...C],R=await Go(t,y),L=[];let A=((r=i.flip)==null?void 0:r.overflows)||[];if(c&&L.push(R[m]),f){const j=cx(o,s,S);L.push(R[j[0]],R[j[1]])}if(A=[...A,{placement:o,overflows:L}],!L.every(j=>j<=0)){var F,I;const j=(((F=i.flip)==null?void 0:F.index)||0)+1,G=P[j];if(G&&(!(f==="alignment"?p!==Ot(G):!1)||A.every(T=>T.overflows[0]>0&&Ot(T.placement)===p)))return{data:{index:j,overflows:A},reset:{placement:G}};let $=(I=A.filter(V=>V.overflows[0]<=0).sort((V,T)=>V.overflows[1]-T.overflows[1])[0])==null?void 0:I.placement;if(!$)switch(d){case"bestFit":{var Q;const V=(Q=A.filter(T=>{if(k){const N=Ot(T.placement);return N===p||N==="y"}return!0}).map(T=>[T.placement,T.overflows.filter(N=>N>0).reduce((N,M)=>N+M,0)]).sort((T,N)=>T[1]-N[1])[0])==null?void 0:Q[0];V&&($=V);break}case"initialPlacement":$=l;break}if(o!==$)return{reset:{placement:$}}}return{}}}};function zd(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Fd(e){return lx.some(t=>e[t]>=0)}const yx=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=Yt(e,t);switch(r){case"referenceHidden":{const i=await Go(t,{...o,elementContext:"reference"}),s=zd(i,n.reference);return{data:{referenceHiddenOffsets:s,referenceHidden:Fd(s)}}}case"escaped":{const i=await Go(t,{...o,altBoundary:!0}),s=zd(i,n.floating);return{data:{escapedOffsets:s,escaped:Fd(s)}}}default:return{}}}}};async function wx(e,t){const{placement:n,platform:r,elements:o}=e,i=await(r.isRTL==null?void 0:r.isRTL(o.floating)),s=Gt(n),l=ro(n),a=Ot(n)==="y",u=["left","top"].includes(s)?-1:1,c=i&&a?-1:1,f=Yt(t,e);let{mainAxis:h,crossAxis:d,alignmentAxis:w}=typeof f=="number"?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return l&&typeof w=="number"&&(d=l==="end"?w*-1:w),a?{x:d*c,y:h*u}:{x:h*u,y:d*c}}const xx=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:s,middlewareData:l}=t,a=await wx(t,e);return s===((n=l.offset)==null?void 0:n.placement)&&(r=l.arrow)!=null&&r.alignmentOffset?{}:{x:o+a.x,y:i+a.y,data:{...a,placement:s}}}}},Sx=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:s=!1,limiter:l={fn:y=>{let{x:m,y:p}=y;return{x:m,y:p}}},...a}=Yt(e,t),u={x:n,y:r},c=await Go(t,a),f=Ot(Gt(o)),h=qu(f);let d=u[h],w=u[f];if(i){const y=h==="y"?"top":"left",m=h==="y"?"bottom":"right",p=d+c[y],g=d-c[m];d=Ba(p,d,g)}if(s){const y=f==="y"?"top":"left",m=f==="y"?"bottom":"right",p=w+c[y],g=w-c[m];w=Ba(p,w,g)}const v=l.fn({...t,[h]:d,[f]:w});return{...v,data:{x:v.x-n,y:v.y-r,enabled:{[h]:i,[f]:s}}}}}},Ex=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:i,middlewareData:s}=t,{offset:l=0,mainAxis:a=!0,crossAxis:u=!0}=Yt(e,t),c={x:n,y:r},f=Ot(o),h=qu(f);let d=c[h],w=c[f];const v=Yt(l,t),y=typeof v=="number"?{mainAxis:v,crossAxis:0}:{mainAxis:0,crossAxis:0,...v};if(a){const g=h==="y"?"height":"width",S=i.reference[h]-i.floating[g]+y.mainAxis,C=i.reference[h]+i.reference[g]-y.mainAxis;d<S?d=S:d>C&&(d=C)}if(u){var m,p;const g=h==="y"?"width":"height",S=["top","left"].includes(Gt(o)),C=i.reference[f]-i.floating[g]+(S&&((m=s.offset)==null?void 0:m[f])||0)+(S?0:y.crossAxis),k=i.reference[f]+i.reference[g]+(S?0:((p=s.offset)==null?void 0:p[f])||0)-(S?y.crossAxis:0);w<C?w=C:w>k&&(w=k)}return{[h]:d,[f]:w}}}},Cx=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:i,platform:s,elements:l}=t,{apply:a=()=>{},...u}=Yt(e,t),c=await Go(t,u),f=Gt(o),h=ro(o),d=Ot(o)==="y",{width:w,height:v}=i.floating;let y,m;f==="top"||f==="bottom"?(y=f,m=h===(await(s.isRTL==null?void 0:s.isRTL(l.floating))?"start":"end")?"left":"right"):(m=f,y=h==="end"?"top":"bottom");const p=v-c.top-c.bottom,g=w-c.left-c.right,S=An(v-c[y],p),C=An(w-c[m],g),k=!t.middlewareData.shift;let P=S,R=C;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(R=g),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(P=p),k&&!h){const A=Qe(c.left,0),F=Qe(c.right,0),I=Qe(c.top,0),Q=Qe(c.bottom,0);d?R=w-2*(A!==0||F!==0?A+F:Qe(c.left,c.right)):P=v-2*(I!==0||Q!==0?I+Q:Qe(c.top,c.bottom))}await a({...t,availableWidth:R,availableHeight:P});const L=await s.getDimensions(l.floating);return w!==L.width||v!==L.height?{reset:{rects:!0}}:{}}}};function Ks(){return typeof window<"u"}function oo(e){return vm(e)?(e.nodeName||"").toLowerCase():"#document"}function Ge(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Mt(e){var t;return(t=(vm(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function vm(e){return Ks()?e instanceof Node||e instanceof Ge(e).Node:!1}function Et(e){return Ks()?e instanceof Element||e instanceof Ge(e).Element:!1}function jt(e){return Ks()?e instanceof HTMLElement||e instanceof Ge(e).HTMLElement:!1}function $d(e){return!Ks()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof Ge(e).ShadowRoot}function ai(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=Ct(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function kx(e){return["table","td","th"].includes(oo(e))}function Ys(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function ec(e){const t=tc(),n=Et(e)?Ct(e):e;return["transform","translate","scale","rotate","perspective"].some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function Px(e){let t=Ln(e);for(;jt(t)&&!qr(t);){if(ec(t))return t;if(Ys(t))return null;t=Ln(t)}return null}function tc(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function qr(e){return["html","body","#document"].includes(oo(e))}function Ct(e){return Ge(e).getComputedStyle(e)}function Gs(e){return Et(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Ln(e){if(oo(e)==="html")return e;const t=e.assignedSlot||e.parentNode||$d(e)&&e.host||Mt(e);return $d(t)?t.host:t}function ym(e){const t=Ln(e);return qr(t)?e.ownerDocument?e.ownerDocument.body:e.body:jt(t)&&ai(t)?t:ym(t)}function Xo(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=ym(e),i=o===((r=e.ownerDocument)==null?void 0:r.body),s=Ge(o);if(i){const l=Wa(s);return t.concat(s,s.visualViewport||[],ai(o)?o:[],l&&n?Xo(l):[])}return t.concat(o,Xo(o,[],n))}function Wa(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function wm(e){const t=Ct(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=jt(e),i=o?e.offsetWidth:n,s=o?e.offsetHeight:r,l=xs(n)!==i||xs(r)!==s;return l&&(n=i,r=s),{width:n,height:r,$:l}}function nc(e){return Et(e)?e:e.contextElement}function jr(e){const t=nc(e);if(!jt(t))return Lt(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=wm(t);let s=(i?xs(n.width):n.width)/r,l=(i?xs(n.height):n.height)/o;return(!s||!Number.isFinite(s))&&(s=1),(!l||!Number.isFinite(l))&&(l=1),{x:s,y:l}}const bx=Lt(0);function xm(e){const t=Ge(e);return!tc()||!t.visualViewport?bx:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Tx(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==Ge(e)?!1:t}function or(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),i=nc(e);let s=Lt(1);t&&(r?Et(r)&&(s=jr(r)):s=jr(e));const l=Tx(i,n,r)?xm(i):Lt(0);let a=(o.left+l.x)/s.x,u=(o.top+l.y)/s.y,c=o.width/s.x,f=o.height/s.y;if(i){const h=Ge(i),d=r&&Et(r)?Ge(r):r;let w=h,v=Wa(w);for(;v&&r&&d!==w;){const y=jr(v),m=v.getBoundingClientRect(),p=Ct(v),g=m.left+(v.clientLeft+parseFloat(p.paddingLeft))*y.x,S=m.top+(v.clientTop+parseFloat(p.paddingTop))*y.y;a*=y.x,u*=y.y,c*=y.x,f*=y.y,a+=g,u+=S,w=Ge(v),v=Wa(w)}}return Es({width:c,height:f,x:a,y:u})}function rc(e,t){const n=Gs(e).scrollLeft;return t?t.left+n:or(Mt(e)).left+n}function Sm(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),o=r.left+t.scrollLeft-(n?0:rc(e,r)),i=r.top+t.scrollTop;return{x:o,y:i}}function Nx(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i=o==="fixed",s=Mt(r),l=t?Ys(t.floating):!1;if(r===s||l&&i)return n;let a={scrollLeft:0,scrollTop:0},u=Lt(1);const c=Lt(0),f=jt(r);if((f||!f&&!i)&&((oo(r)!=="body"||ai(s))&&(a=Gs(r)),jt(r))){const d=or(r);u=jr(r),c.x=d.x+r.clientLeft,c.y=d.y+r.clientTop}const h=s&&!f&&!i?Sm(s,a,!0):Lt(0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-a.scrollLeft*u.x+c.x+h.x,y:n.y*u.y-a.scrollTop*u.y+c.y+h.y}}function Rx(e){return Array.from(e.getClientRects())}function Ox(e){const t=Mt(e),n=Gs(e),r=e.ownerDocument.body,o=Qe(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=Qe(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let s=-n.scrollLeft+rc(e);const l=-n.scrollTop;return Ct(r).direction==="rtl"&&(s+=Qe(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:s,y:l}}function _x(e,t){const n=Ge(e),r=Mt(e),o=n.visualViewport;let i=r.clientWidth,s=r.clientHeight,l=0,a=0;if(o){i=o.width,s=o.height;const u=tc();(!u||u&&t==="fixed")&&(l=o.offsetLeft,a=o.offsetTop)}return{width:i,height:s,x:l,y:a}}function Ax(e,t){const n=or(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=jt(e)?jr(e):Lt(1),s=e.clientWidth*i.x,l=e.clientHeight*i.y,a=o*i.x,u=r*i.y;return{width:s,height:l,x:a,y:u}}function Ud(e,t,n){let r;if(t==="viewport")r=_x(e,n);else if(t==="document")r=Ox(Mt(e));else if(Et(t))r=Ax(t,n);else{const o=xm(e);r={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return Es(r)}function Em(e,t){const n=Ln(e);return n===t||!Et(n)||qr(n)?!1:Ct(n).position==="fixed"||Em(n,t)}function Lx(e,t){const n=t.get(e);if(n)return n;let r=Xo(e,[],!1).filter(l=>Et(l)&&oo(l)!=="body"),o=null;const i=Ct(e).position==="fixed";let s=i?Ln(e):e;for(;Et(s)&&!qr(s);){const l=Ct(s),a=ec(s);!a&&l.position==="fixed"&&(o=null),(i?!a&&!o:!a&&l.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||ai(s)&&!a&&Em(e,s))?r=r.filter(c=>c!==s):o=l,s=Ln(s)}return t.set(e,r),r}function jx(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const s=[...n==="clippingAncestors"?Ys(t)?[]:Lx(t,this._c):[].concat(n),r],l=s[0],a=s.reduce((u,c)=>{const f=Ud(t,c,o);return u.top=Qe(f.top,u.top),u.right=An(f.right,u.right),u.bottom=An(f.bottom,u.bottom),u.left=Qe(f.left,u.left),u},Ud(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}}function Mx(e){const{width:t,height:n}=wm(e);return{width:t,height:n}}function Dx(e,t,n){const r=jt(t),o=Mt(t),i=n==="fixed",s=or(e,!0,i,t);let l={scrollLeft:0,scrollTop:0};const a=Lt(0);function u(){a.x=rc(o)}if(r||!r&&!i)if((oo(t)!=="body"||ai(o))&&(l=Gs(t)),r){const d=or(t,!0,i,t);a.x=d.x+t.clientLeft,a.y=d.y+t.clientTop}else o&&u();i&&!r&&o&&u();const c=o&&!r&&!i?Sm(o,l):Lt(0),f=s.left+l.scrollLeft-a.x-c.x,h=s.top+l.scrollTop-a.y-c.y;return{x:f,y:h,width:s.width,height:s.height}}function zl(e){return Ct(e).position==="static"}function Bd(e,t){if(!jt(e)||Ct(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Mt(e)===n&&(n=n.ownerDocument.body),n}function Cm(e,t){const n=Ge(e);if(Ys(e))return n;if(!jt(e)){let o=Ln(e);for(;o&&!qr(o);){if(Et(o)&&!zl(o))return o;o=Ln(o)}return n}let r=Bd(e,t);for(;r&&kx(r)&&zl(r);)r=Bd(r,t);return r&&qr(r)&&zl(r)&&!ec(r)?n:r||Px(e)||n}const Ix=async function(e){const t=this.getOffsetParent||Cm,n=this.getDimensions,r=await n(e.floating);return{reference:Dx(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function zx(e){return Ct(e).direction==="rtl"}const Fx={convertOffsetParentRelativeRectToViewportRelativeRect:Nx,getDocumentElement:Mt,getClippingRect:jx,getOffsetParent:Cm,getElementRects:Ix,getClientRects:Rx,getDimensions:Mx,getScale:jr,isElement:Et,isRTL:zx};function km(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function $x(e,t){let n=null,r;const o=Mt(e);function i(){var l;clearTimeout(r),(l=n)==null||l.disconnect(),n=null}function s(l,a){l===void 0&&(l=!1),a===void 0&&(a=1),i();const u=e.getBoundingClientRect(),{left:c,top:f,width:h,height:d}=u;if(l||t(),!h||!d)return;const w=Li(f),v=Li(o.clientWidth-(c+h)),y=Li(o.clientHeight-(f+d)),m=Li(c),g={rootMargin:-w+"px "+-v+"px "+-y+"px "+-m+"px",threshold:Qe(0,An(1,a))||1};let S=!0;function C(k){const P=k[0].intersectionRatio;if(P!==a){if(!S)return s();P?s(!1,P):r=setTimeout(()=>{s(!1,1e-7)},1e3)}P===1&&!km(u,e.getBoundingClientRect())&&s(),S=!1}try{n=new IntersectionObserver(C,{...g,root:o.ownerDocument})}catch{n=new IntersectionObserver(C,g)}n.observe(e)}return s(!0),i}function Ux(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:l=typeof IntersectionObserver=="function",animationFrame:a=!1}=r,u=nc(e),c=o||i?[...u?Xo(u):[],...Xo(t)]:[];c.forEach(m=>{o&&m.addEventListener("scroll",n,{passive:!0}),i&&m.addEventListener("resize",n)});const f=u&&l?$x(u,n):null;let h=-1,d=null;s&&(d=new ResizeObserver(m=>{let[p]=m;p&&p.target===u&&d&&(d.unobserve(t),cancelAnimationFrame(h),h=requestAnimationFrame(()=>{var g;(g=d)==null||g.observe(t)})),n()}),u&&!a&&d.observe(u),d.observe(t));let w,v=a?or(e):null;a&&y();function y(){const m=or(e);v&&!km(v,m)&&n(),v=m,w=requestAnimationFrame(y)}return n(),()=>{var m;c.forEach(p=>{o&&p.removeEventListener("scroll",n),i&&p.removeEventListener("resize",n)}),f==null||f(),(m=d)==null||m.disconnect(),d=null,a&&cancelAnimationFrame(w)}}const Bx=xx,Vx=Sx,Wx=vx,Hx=Cx,Qx=yx,Vd=gx,Kx=Ex,Yx=(e,t,n)=>{const r=new Map,o={platform:Fx,...n},i={...o.platform,_c:r};return mx(e,t,{...o,platform:i})};var Gx=typeof document<"u",Xx=function(){},Gi=Gx?x.useLayoutEffect:Xx;function Cs(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!Cs(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const i=o[r];if(!(i==="_owner"&&e.$$typeof)&&!Cs(e[i],t[i]))return!1}return!0}return e!==e&&t!==t}function Pm(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Wd(e,t){const n=Pm(e);return Math.round(t*n)/n}function Fl(e){const t=x.useRef(e);return Gi(()=>{t.current=e}),t}function qx(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:s}={},transform:l=!0,whileElementsMounted:a,open:u}=e,[c,f]=x.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,d]=x.useState(r);Cs(h,r)||d(r);const[w,v]=x.useState(null),[y,m]=x.useState(null),p=x.useCallback(T=>{T!==k.current&&(k.current=T,v(T))},[]),g=x.useCallback(T=>{T!==P.current&&(P.current=T,m(T))},[]),S=i||w,C=s||y,k=x.useRef(null),P=x.useRef(null),R=x.useRef(c),L=a!=null,A=Fl(a),F=Fl(o),I=Fl(u),Q=x.useCallback(()=>{if(!k.current||!P.current)return;const T={placement:t,strategy:n,middleware:h};F.current&&(T.platform=F.current),Yx(k.current,P.current,T).then(N=>{const M={...N,isPositioned:I.current!==!1};j.current&&!Cs(R.current,M)&&(R.current=M,si.flushSync(()=>{f(M)}))})},[h,t,n,F,I]);Gi(()=>{u===!1&&R.current.isPositioned&&(R.current.isPositioned=!1,f(T=>({...T,isPositioned:!1})))},[u]);const j=x.useRef(!1);Gi(()=>(j.current=!0,()=>{j.current=!1}),[]),Gi(()=>{if(S&&(k.current=S),C&&(P.current=C),S&&C){if(A.current)return A.current(S,C,Q);Q()}},[S,C,Q,A,L]);const G=x.useMemo(()=>({reference:k,floating:P,setReference:p,setFloating:g}),[p,g]),$=x.useMemo(()=>({reference:S,floating:C}),[S,C]),V=x.useMemo(()=>{const T={position:n,left:0,top:0};if(!$.floating)return T;const N=Wd($.floating,c.x),M=Wd($.floating,c.y);return l?{...T,transform:"translate("+N+"px, "+M+"px)",...Pm($.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:N,top:M}},[n,l,$.floating,c.x,c.y]);return x.useMemo(()=>({...c,update:Q,refs:G,elements:$,floatingStyles:V}),[c,Q,G,$,V])}const Zx=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?Vd({element:r.current,padding:o}).fn(n):{}:r?Vd({element:r,padding:o}).fn(n):{}}}},Jx=(e,t)=>({...Bx(e),options:[e,t]}),e1=(e,t)=>({...Vx(e),options:[e,t]}),t1=(e,t)=>({...Kx(e),options:[e,t]}),n1=(e,t)=>({...Wx(e),options:[e,t]}),r1=(e,t)=>({...Hx(e),options:[e,t]}),o1=(e,t)=>({...Qx(e),options:[e,t]}),i1=(e,t)=>({...Zx(e),options:[e,t]});var s1="Arrow",bm=x.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...i}=e;return E.jsx(We.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:E.jsx("polygon",{points:"0,0 30,0 15,10"})})});bm.displayName=s1;var l1=bm;function a1(e){const[t,n]=x.useState(void 0);return _n(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const i=o[0];let s,l;if("borderBoxSize"in i){const a=i.borderBoxSize,u=Array.isArray(a)?a[0]:a;s=u.inlineSize,l=u.blockSize}else s=e.offsetWidth,l=e.offsetHeight;n({width:s,height:l})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var Tm="Popper",[Nm,Rm]=Vs(Tm),[sE,Om]=Nm(Tm),_m="PopperAnchor",Am=x.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,i=Om(_m,n),s=x.useRef(null),l=St(t,s);return x.useEffect(()=>{i.onAnchorChange((r==null?void 0:r.current)||s.current)}),r?null:E.jsx(We.div,{...o,ref:l})});Am.displayName=_m;var oc="PopperContent",[u1,c1]=Nm(oc),Lm=x.forwardRef((e,t)=>{var J,ar,qt,In,Zt,ur;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:i="center",alignOffset:s=0,arrowPadding:l=0,avoidCollisions:a=!0,collisionBoundary:u=[],collisionPadding:c=0,sticky:f="partial",hideWhenDetached:h=!1,updatePositionStrategy:d="optimized",onPlaced:w,...v}=e,y=Om(oc,n),[m,p]=x.useState(null),g=St(t,Jt=>p(Jt)),[S,C]=x.useState(null),k=a1(S),P=(k==null?void 0:k.width)??0,R=(k==null?void 0:k.height)??0,L=r+(i!=="center"?"-"+i:""),A=typeof c=="number"?c:{top:0,right:0,bottom:0,left:0,...c},F=Array.isArray(u)?u:[u],I=F.length>0,Q={padding:A,boundary:F.filter(f1),altBoundary:I},{refs:j,floatingStyles:G,placement:$,isPositioned:V,middlewareData:T}=qx({strategy:"fixed",placement:L,whileElementsMounted:(...Jt)=>Ux(...Jt,{animationFrame:d==="always"}),elements:{reference:y.anchor},middleware:[Jx({mainAxis:o+R,alignmentAxis:s}),a&&e1({mainAxis:!0,crossAxis:!1,limiter:f==="partial"?t1():void 0,...Q}),a&&n1({...Q}),r1({...Q,apply:({elements:Jt,rects:ui,availableWidth:rl,availableHeight:ci})=>{const{width:ol,height:io}=ui.reference,cr=Jt.floating.style;cr.setProperty("--radix-popper-available-width",`${rl}px`),cr.setProperty("--radix-popper-available-height",`${ci}px`),cr.setProperty("--radix-popper-anchor-width",`${ol}px`),cr.setProperty("--radix-popper-anchor-height",`${io}px`)}}),S&&i1({element:S,padding:l}),p1({arrowWidth:P,arrowHeight:R}),h&&o1({strategy:"referenceHidden",...Q})]}),[N,M]=Dm($),W=On(w);_n(()=>{V&&(W==null||W())},[V,W]);const z=(J=T.arrow)==null?void 0:J.x,K=(ar=T.arrow)==null?void 0:ar.y,X=((qt=T.arrow)==null?void 0:qt.centerOffset)!==0,[he,ke]=x.useState();return _n(()=>{m&&ke(window.getComputedStyle(m).zIndex)},[m]),E.jsx("div",{ref:j.setFloating,"data-radix-popper-content-wrapper":"",style:{...G,transform:V?G.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:he,"--radix-popper-transform-origin":[(In=T.transformOrigin)==null?void 0:In.x,(Zt=T.transformOrigin)==null?void 0:Zt.y].join(" "),...((ur=T.hide)==null?void 0:ur.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:E.jsx(u1,{scope:n,placedSide:N,onArrowChange:C,arrowX:z,arrowY:K,shouldHideArrow:X,children:E.jsx(We.div,{"data-side":N,"data-align":M,...v,ref:g,style:{...v.style,animation:V?void 0:"none"}})})})});Lm.displayName=oc;var jm="PopperArrow",d1={top:"bottom",right:"left",bottom:"top",left:"right"},Mm=x.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,i=c1(jm,r),s=d1[i.placedSide];return E.jsx("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[s]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:E.jsx(l1,{...o,ref:n,style:{...o.style,display:"block"}})})});Mm.displayName=jm;function f1(e){return e!==null}var p1=e=>({name:"transformOrigin",options:e,fn(t){var y,m,p;const{placement:n,rects:r,middlewareData:o}=t,s=((y=o.arrow)==null?void 0:y.centerOffset)!==0,l=s?0:e.arrowWidth,a=s?0:e.arrowHeight,[u,c]=Dm(n),f={start:"0%",center:"50%",end:"100%"}[c],h=(((m=o.arrow)==null?void 0:m.x)??0)+l/2,d=(((p=o.arrow)==null?void 0:p.y)??0)+a/2;let w="",v="";return u==="bottom"?(w=s?f:`${h}px`,v=`${-a}px`):u==="top"?(w=s?f:`${h}px`,v=`${r.floating.height+a}px`):u==="right"?(w=`${-a}px`,v=s?f:`${d}px`):u==="left"&&(w=`${r.floating.width+a}px`,v=s?f:`${d}px`),{data:{x:w,y:v}}}});function Dm(e){const[t,n="center"]=e.split("-");return[t,n]}var h1=Am,m1=Lm,g1=Mm,[Xs,lE]=Vs("Tooltip",[Rm]),ic=Rm(),Im="TooltipProvider",v1=700,Hd="tooltip.open",[y1,zm]=Xs(Im),Fm=e=>{const{__scopeTooltip:t,delayDuration:n=v1,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:i}=e,s=x.useRef(!0),l=x.useRef(!1),a=x.useRef(0);return x.useEffect(()=>{const u=a.current;return()=>window.clearTimeout(u)},[]),E.jsx(y1,{scope:t,isOpenDelayedRef:s,delayDuration:n,onOpen:x.useCallback(()=>{window.clearTimeout(a.current),s.current=!1},[]),onClose:x.useCallback(()=>{window.clearTimeout(a.current),a.current=window.setTimeout(()=>s.current=!0,r)},[r]),isPointerInTransitRef:l,onPointerInTransitChange:x.useCallback(u=>{l.current=u},[]),disableHoverableContent:o,children:i})};Fm.displayName=Im;var $m="Tooltip",[aE,qs]=Xs($m),Ha="TooltipTrigger",w1=x.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=qs(Ha,n),i=zm(Ha,n),s=ic(n),l=x.useRef(null),a=St(t,l,o.onTriggerChange),u=x.useRef(!1),c=x.useRef(!1),f=x.useCallback(()=>u.current=!1,[]);return x.useEffect(()=>()=>document.removeEventListener("pointerup",f),[f]),E.jsx(h1,{asChild:!0,...s,children:E.jsx(We.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:a,onPointerMove:ve(e.onPointerMove,h=>{h.pointerType!=="touch"&&!c.current&&!i.isPointerInTransitRef.current&&(o.onTriggerEnter(),c.current=!0)}),onPointerLeave:ve(e.onPointerLeave,()=>{o.onTriggerLeave(),c.current=!1}),onPointerDown:ve(e.onPointerDown,()=>{o.open&&o.onClose(),u.current=!0,document.addEventListener("pointerup",f,{once:!0})}),onFocus:ve(e.onFocus,()=>{u.current||o.onOpen()}),onBlur:ve(e.onBlur,o.onClose),onClick:ve(e.onClick,o.onClose)})})});w1.displayName=Ha;var x1="TooltipPortal",[uE,S1]=Xs(x1,{forceMount:void 0}),Zr="TooltipContent",Um=x.forwardRef((e,t)=>{const n=S1(Zr,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,s=qs(Zr,e.__scopeTooltip);return E.jsx(Qu,{present:r||s.open,children:s.disableHoverableContent?E.jsx(Bm,{side:o,...i,ref:t}):E.jsx(E1,{side:o,...i,ref:t})})}),E1=x.forwardRef((e,t)=>{const n=qs(Zr,e.__scopeTooltip),r=zm(Zr,e.__scopeTooltip),o=x.useRef(null),i=St(t,o),[s,l]=x.useState(null),{trigger:a,onClose:u}=n,c=o.current,{onPointerInTransitChange:f}=r,h=x.useCallback(()=>{l(null),f(!1)},[f]),d=x.useCallback((w,v)=>{const y=w.currentTarget,m={x:w.clientX,y:w.clientY},p=T1(m,y.getBoundingClientRect()),g=N1(m,p),S=R1(v.getBoundingClientRect()),C=_1([...g,...S]);l(C),f(!0)},[f]);return x.useEffect(()=>()=>h(),[h]),x.useEffect(()=>{if(a&&c){const w=y=>d(y,c),v=y=>d(y,a);return a.addEventListener("pointerleave",w),c.addEventListener("pointerleave",v),()=>{a.removeEventListener("pointerleave",w),c.removeEventListener("pointerleave",v)}}},[a,c,d,h]),x.useEffect(()=>{if(s){const w=v=>{const y=v.target,m={x:v.clientX,y:v.clientY},p=(a==null?void 0:a.contains(y))||(c==null?void 0:c.contains(y)),g=!O1(m,s);p?h():g&&(h(),u())};return document.addEventListener("pointermove",w),()=>document.removeEventListener("pointermove",w)}},[a,c,s,u,h]),E.jsx(Bm,{...e,ref:i})}),[C1,k1]=Xs($m,{isInside:!1}),P1=qy("TooltipContent"),Bm=x.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:i,onPointerDownOutside:s,...l}=e,a=qs(Zr,n),u=ic(n),{onClose:c}=a;return x.useEffect(()=>(document.addEventListener(Hd,c),()=>document.removeEventListener(Hd,c)),[c]),x.useEffect(()=>{if(a.trigger){const f=h=>{const d=h.target;d!=null&&d.contains(a.trigger)&&c()};return window.addEventListener("scroll",f,{capture:!0}),()=>window.removeEventListener("scroll",f,{capture:!0})}},[a.trigger,c]),E.jsx(Hu,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:f=>f.preventDefault(),onDismiss:c,children:E.jsxs(m1,{"data-state":a.stateAttribute,...u,...l,ref:t,style:{...l.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[E.jsx(P1,{children:r}),E.jsx(C1,{scope:n,isInside:!0,children:E.jsx(E0,{id:a.contentId,role:"tooltip",children:o||r})})]})})});Um.displayName=Zr;var Vm="TooltipArrow",b1=x.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=ic(n);return k1(Vm,n).isInside?null:E.jsx(g1,{...o,...r,ref:t})});b1.displayName=Vm;function T1(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function N1(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function R1(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function O1(e,t){const{x:n,y:r}=e;let o=!1;for(let i=0,s=t.length-1;i<t.length;s=i++){const l=t[i],a=t[s],u=l.x,c=l.y,f=a.x,h=a.y;c>r!=h>r&&n<(f-u)*(r-c)/(h-c)+u&&(o=!o)}return o}function _1(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),A1(t)}function A1(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const i=t[t.length-1],s=t[t.length-2];if((i.x-s.x)*(o.y-s.y)>=(i.y-s.y)*(o.x-s.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const i=n[n.length-1],s=n[n.length-2];if((i.x-s.x)*(o.y-s.y)>=(i.y-s.y)*(o.x-s.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var L1=Fm,Wm=Um;const j1=L1,M1=x.forwardRef(({className:e,sideOffset:t=4,...n},r)=>E.jsx(Wm,{ref:r,sideOffset:t,className:lr("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n}));M1.displayName=Wm.displayName;var Zs=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Js=typeof window>"u"||"Deno"in globalThis;function pt(){}function D1(e,t){return typeof e=="function"?e(t):e}function I1(e){return typeof e=="number"&&e>=0&&e!==1/0}function z1(e,t){return Math.max(e+(t||0)-Date.now(),0)}function Qa(e,t){return typeof e=="function"?e(t):e}function F1(e,t){return typeof e=="function"?e(t):e}function Qd(e,t){const{type:n="all",exact:r,fetchStatus:o,predicate:i,queryKey:s,stale:l}=e;if(s){if(r){if(t.queryHash!==sc(s,t.options))return!1}else if(!Zo(t.queryKey,s))return!1}if(n!=="all"){const a=t.isActive();if(n==="active"&&!a||n==="inactive"&&a)return!1}return!(typeof l=="boolean"&&t.isStale()!==l||o&&o!==t.state.fetchStatus||i&&!i(t))}function Kd(e,t){const{exact:n,status:r,predicate:o,mutationKey:i}=e;if(i){if(!t.options.mutationKey)return!1;if(n){if(qo(t.options.mutationKey)!==qo(i))return!1}else if(!Zo(t.options.mutationKey,i))return!1}return!(r&&t.state.status!==r||o&&!o(t))}function sc(e,t){return((t==null?void 0:t.queryKeyHashFn)||qo)(e)}function qo(e){return JSON.stringify(e,(t,n)=>Ka(n)?Object.keys(n).sort().reduce((r,o)=>(r[o]=n[o],r),{}):n)}function Zo(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?Object.keys(t).every(n=>Zo(e[n],t[n])):!1}function Hm(e,t){if(e===t)return e;const n=Yd(e)&&Yd(t);if(n||Ka(e)&&Ka(t)){const r=n?e:Object.keys(e),o=r.length,i=n?t:Object.keys(t),s=i.length,l=n?[]:{},a=new Set(r);let u=0;for(let c=0;c<s;c++){const f=n?c:i[c];(!n&&a.has(f)||n)&&e[f]===void 0&&t[f]===void 0?(l[f]=void 0,u++):(l[f]=Hm(e[f],t[f]),l[f]===e[f]&&e[f]!==void 0&&u++)}return o===s&&u===o?e:l}return t}function Yd(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function Ka(e){if(!Gd(e))return!1;const t=e.constructor;if(t===void 0)return!0;const n=t.prototype;return!(!Gd(n)||!n.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function Gd(e){return Object.prototype.toString.call(e)==="[object Object]"}function $1(e){return new Promise(t=>{setTimeout(t,e)})}function U1(e,t,n){return typeof n.structuralSharing=="function"?n.structuralSharing(e,t):n.structuralSharing!==!1?Hm(e,t):t}function B1(e,t,n=0){const r=[...e,t];return n&&r.length>n?r.slice(1):r}function V1(e,t,n=0){const r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}var lc=Symbol();function Qm(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===lc?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}var Hn,fn,Dr,lf,W1=(lf=class extends Zs{constructor(){super();Z(this,Hn);Z(this,fn);Z(this,Dr);B(this,Dr,t=>{if(!Js&&window.addEventListener){const n=()=>t();return window.addEventListener("visibilitychange",n,!1),()=>{window.removeEventListener("visibilitychange",n)}}})}onSubscribe(){b(this,fn)||this.setEventListener(b(this,Dr))}onUnsubscribe(){var t;this.hasListeners()||((t=b(this,fn))==null||t.call(this),B(this,fn,void 0))}setEventListener(t){var n;B(this,Dr,t),(n=b(this,fn))==null||n.call(this),B(this,fn,t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(t){b(this,Hn)!==t&&(B(this,Hn,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(n=>{n(t)})}isFocused(){var t;return typeof b(this,Hn)=="boolean"?b(this,Hn):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},Hn=new WeakMap,fn=new WeakMap,Dr=new WeakMap,lf),Km=new W1,Ir,pn,zr,af,H1=(af=class extends Zs{constructor(){super();Z(this,Ir,!0);Z(this,pn);Z(this,zr);B(this,zr,t=>{if(!Js&&window.addEventListener){const n=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",n,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",n),window.removeEventListener("offline",r)}}})}onSubscribe(){b(this,pn)||this.setEventListener(b(this,zr))}onUnsubscribe(){var t;this.hasListeners()||((t=b(this,pn))==null||t.call(this),B(this,pn,void 0))}setEventListener(t){var n;B(this,zr,t),(n=b(this,pn))==null||n.call(this),B(this,pn,t(this.setOnline.bind(this)))}setOnline(t){b(this,Ir)!==t&&(B(this,Ir,t),this.listeners.forEach(r=>{r(t)}))}isOnline(){return b(this,Ir)}},Ir=new WeakMap,pn=new WeakMap,zr=new WeakMap,af),ks=new H1;function Q1(){let e,t;const n=new Promise((o,i)=>{e=o,t=i});n.status="pending",n.catch(()=>{});function r(o){Object.assign(n,o),delete n.resolve,delete n.reject}return n.resolve=o=>{r({status:"fulfilled",value:o}),e(o)},n.reject=o=>{r({status:"rejected",reason:o}),t(o)},n}function K1(e){return Math.min(1e3*2**e,3e4)}function Ym(e){return(e??"online")==="online"?ks.isOnline():!0}var Gm=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function $l(e){return e instanceof Gm}function Xm(e){let t=!1,n=0,r=!1,o;const i=Q1(),s=v=>{var y;r||(h(new Gm(v)),(y=e.abort)==null||y.call(e))},l=()=>{t=!0},a=()=>{t=!1},u=()=>Km.isFocused()&&(e.networkMode==="always"||ks.isOnline())&&e.canRun(),c=()=>Ym(e.networkMode)&&e.canRun(),f=v=>{var y;r||(r=!0,(y=e.onSuccess)==null||y.call(e,v),o==null||o(),i.resolve(v))},h=v=>{var y;r||(r=!0,(y=e.onError)==null||y.call(e,v),o==null||o(),i.reject(v))},d=()=>new Promise(v=>{var y;o=m=>{(r||u())&&v(m)},(y=e.onPause)==null||y.call(e)}).then(()=>{var v;o=void 0,r||(v=e.onContinue)==null||v.call(e)}),w=()=>{if(r)return;let v;const y=n===0?e.initialPromise:void 0;try{v=y??e.fn()}catch(m){v=Promise.reject(m)}Promise.resolve(v).then(f).catch(m=>{var k;if(r)return;const p=e.retry??(Js?0:3),g=e.retryDelay??K1,S=typeof g=="function"?g(n,m):g,C=p===!0||typeof p=="number"&&n<p||typeof p=="function"&&p(n,m);if(t||!C){h(m);return}n++,(k=e.onFail)==null||k.call(e,n,m),$1(S).then(()=>u()?void 0:d()).then(()=>{t?h(m):w()})})};return{promise:i,cancel:s,continue:()=>(o==null||o(),i),cancelRetry:l,continueRetry:a,canStart:c,start:()=>(c()?w():d().then(w),i)}}var Y1=e=>setTimeout(e,0);function G1(){let e=[],t=0,n=l=>{l()},r=l=>{l()},o=Y1;const i=l=>{t?e.push(l):o(()=>{n(l)})},s=()=>{const l=e;e=[],l.length&&o(()=>{r(()=>{l.forEach(a=>{n(a)})})})};return{batch:l=>{let a;t++;try{a=l()}finally{t--,t||s()}return a},batchCalls:l=>(...a)=>{i(()=>{l(...a)})},schedule:i,setNotifyFunction:l=>{n=l},setBatchNotifyFunction:l=>{r=l},setScheduler:l=>{o=l}}}var Le=G1(),Qn,uf,qm=(uf=class{constructor(){Z(this,Qn)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),I1(this.gcTime)&&B(this,Qn,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(Js?1/0:5*60*1e3))}clearGcTimeout(){b(this,Qn)&&(clearTimeout(b(this,Qn)),B(this,Qn,void 0))}},Qn=new WeakMap,uf),Fr,Kn,et,Yn,Ne,Jo,Gn,ht,It,cf,X1=(cf=class extends qm{constructor(t){super();Z(this,ht);Z(this,Fr);Z(this,Kn);Z(this,et);Z(this,Yn);Z(this,Ne);Z(this,Jo);Z(this,Gn);B(this,Gn,!1),B(this,Jo,t.defaultOptions),this.setOptions(t.options),this.observers=[],B(this,Yn,t.client),B(this,et,b(this,Yn).getQueryCache()),this.queryKey=t.queryKey,this.queryHash=t.queryHash,B(this,Fr,Z1(this.options)),this.state=t.state??b(this,Fr),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=b(this,Ne))==null?void 0:t.promise}setOptions(t){this.options={...b(this,Jo),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&b(this,et).remove(this)}setData(t,n){const r=U1(this.state.data,t,this.options);return Pe(this,ht,It).call(this,{data:r,type:"success",dataUpdatedAt:n==null?void 0:n.updatedAt,manual:n==null?void 0:n.manual}),r}setState(t,n){Pe(this,ht,It).call(this,{type:"setState",state:t,setStateOptions:n})}cancel(t){var r,o;const n=(r=b(this,Ne))==null?void 0:r.promise;return(o=b(this,Ne))==null||o.cancel(t),n?n.then(pt).catch(pt):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(b(this,Fr))}isActive(){return this.observers.some(t=>F1(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===lc||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(t=>Qa(t.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(t=0){return this.state.data===void 0?!0:t==="static"?!1:this.state.isInvalidated?!0:!z1(this.state.dataUpdatedAt,t)}onFocus(){var n;const t=this.observers.find(r=>r.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(n=b(this,Ne))==null||n.continue()}onOnline(){var n;const t=this.observers.find(r=>r.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(n=b(this,Ne))==null||n.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),b(this,et).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(n=>n!==t),this.observers.length||(b(this,Ne)&&(b(this,Gn)?b(this,Ne).cancel({revert:!0}):b(this,Ne).cancelRetry()),this.scheduleGc()),b(this,et).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||Pe(this,ht,It).call(this,{type:"invalidate"})}fetch(t,n){var u,c,f;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(n!=null&&n.cancelRefetch))this.cancel({silent:!0});else if(b(this,Ne))return b(this,Ne).continueRetry(),b(this,Ne).promise}if(t&&this.setOptions(t),!this.options.queryFn){const h=this.observers.find(d=>d.options.queryFn);h&&this.setOptions(h.options)}const r=new AbortController,o=h=>{Object.defineProperty(h,"signal",{enumerable:!0,get:()=>(B(this,Gn,!0),r.signal)})},i=()=>{const h=Qm(this.options,n),w=(()=>{const v={client:b(this,Yn),queryKey:this.queryKey,meta:this.meta};return o(v),v})();return B(this,Gn,!1),this.options.persister?this.options.persister(h,w,this):h(w)},l=(()=>{const h={fetchOptions:n,options:this.options,queryKey:this.queryKey,client:b(this,Yn),state:this.state,fetchFn:i};return o(h),h})();(u=this.options.behavior)==null||u.onFetch(l,this),B(this,Kn,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((c=l.fetchOptions)==null?void 0:c.meta))&&Pe(this,ht,It).call(this,{type:"fetch",meta:(f=l.fetchOptions)==null?void 0:f.meta});const a=h=>{var d,w,v,y;$l(h)&&h.silent||Pe(this,ht,It).call(this,{type:"error",error:h}),$l(h)||((w=(d=b(this,et).config).onError)==null||w.call(d,h,this),(y=(v=b(this,et).config).onSettled)==null||y.call(v,this.state.data,h,this)),this.scheduleGc()};return B(this,Ne,Xm({initialPromise:n==null?void 0:n.initialPromise,fn:l.fetchFn,abort:r.abort.bind(r),onSuccess:h=>{var d,w,v,y;if(h===void 0){a(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(h)}catch(m){a(m);return}(w=(d=b(this,et).config).onSuccess)==null||w.call(d,h,this),(y=(v=b(this,et).config).onSettled)==null||y.call(v,h,this.state.error,this),this.scheduleGc()},onError:a,onFail:(h,d)=>{Pe(this,ht,It).call(this,{type:"failed",failureCount:h,error:d})},onPause:()=>{Pe(this,ht,It).call(this,{type:"pause"})},onContinue:()=>{Pe(this,ht,It).call(this,{type:"continue"})},retry:l.options.retry,retryDelay:l.options.retryDelay,networkMode:l.options.networkMode,canRun:()=>!0})),b(this,Ne).start()}},Fr=new WeakMap,Kn=new WeakMap,et=new WeakMap,Yn=new WeakMap,Ne=new WeakMap,Jo=new WeakMap,Gn=new WeakMap,ht=new WeakSet,It=function(t){const n=r=>{switch(t.type){case"failed":return{...r,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...q1(r.data,this.options),fetchMeta:t.meta??null};case"success":return B(this,Kn,void 0),{...r,data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const o=t.error;return $l(o)&&o.revert&&b(this,Kn)?{...b(this,Kn),fetchStatus:"idle"}:{...r,error:o,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:o,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...t.state}}};this.state=n(this.state),Le.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),b(this,et).notify({query:this,type:"updated",action:t})})},cf);function q1(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Ym(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function Z1(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,n=t!==void 0,r=n?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:n?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}var bt,df,J1=(df=class extends Zs{constructor(t={}){super();Z(this,bt);this.config=t,B(this,bt,new Map)}build(t,n,r){const o=n.queryKey,i=n.queryHash??sc(o,n);let s=this.get(i);return s||(s=new X1({client:t,queryKey:o,queryHash:i,options:t.defaultQueryOptions(n),state:r,defaultOptions:t.getQueryDefaults(o)}),this.add(s)),s}add(t){b(this,bt).has(t.queryHash)||(b(this,bt).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const n=b(this,bt).get(t.queryHash);n&&(t.destroy(),n===t&&b(this,bt).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){Le.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return b(this,bt).get(t)}getAll(){return[...b(this,bt).values()]}find(t){const n={exact:!0,...t};return this.getAll().find(r=>Qd(n,r))}findAll(t={}){const n=this.getAll();return Object.keys(t).length>0?n.filter(r=>Qd(t,r)):n}notify(t){Le.batch(()=>{this.listeners.forEach(n=>{n(t)})})}onFocus(){Le.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){Le.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},bt=new WeakMap,df),Tt,_e,Xn,Nt,ln,ff,eS=(ff=class extends qm{constructor(t){super();Z(this,Nt);Z(this,Tt);Z(this,_e);Z(this,Xn);this.mutationId=t.mutationId,B(this,_e,t.mutationCache),B(this,Tt,[]),this.state=t.state||tS(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){b(this,Tt).includes(t)||(b(this,Tt).push(t),this.clearGcTimeout(),b(this,_e).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){B(this,Tt,b(this,Tt).filter(n=>n!==t)),this.scheduleGc(),b(this,_e).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){b(this,Tt).length||(this.state.status==="pending"?this.scheduleGc():b(this,_e).remove(this))}continue(){var t;return((t=b(this,Xn))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var i,s,l,a,u,c,f,h,d,w,v,y,m,p,g,S,C,k,P,R;const n=()=>{Pe(this,Nt,ln).call(this,{type:"continue"})};B(this,Xn,Xm({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(L,A)=>{Pe(this,Nt,ln).call(this,{type:"failed",failureCount:L,error:A})},onPause:()=>{Pe(this,Nt,ln).call(this,{type:"pause"})},onContinue:n,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>b(this,_e).canRun(this)}));const r=this.state.status==="pending",o=!b(this,Xn).canStart();try{if(r)n();else{Pe(this,Nt,ln).call(this,{type:"pending",variables:t,isPaused:o}),await((s=(i=b(this,_e).config).onMutate)==null?void 0:s.call(i,t,this));const A=await((a=(l=this.options).onMutate)==null?void 0:a.call(l,t));A!==this.state.context&&Pe(this,Nt,ln).call(this,{type:"pending",context:A,variables:t,isPaused:o})}const L=await b(this,Xn).start();return await((c=(u=b(this,_e).config).onSuccess)==null?void 0:c.call(u,L,t,this.state.context,this)),await((h=(f=this.options).onSuccess)==null?void 0:h.call(f,L,t,this.state.context)),await((w=(d=b(this,_e).config).onSettled)==null?void 0:w.call(d,L,null,this.state.variables,this.state.context,this)),await((y=(v=this.options).onSettled)==null?void 0:y.call(v,L,null,t,this.state.context)),Pe(this,Nt,ln).call(this,{type:"success",data:L}),L}catch(L){try{throw await((p=(m=b(this,_e).config).onError)==null?void 0:p.call(m,L,t,this.state.context,this)),await((S=(g=this.options).onError)==null?void 0:S.call(g,L,t,this.state.context)),await((k=(C=b(this,_e).config).onSettled)==null?void 0:k.call(C,void 0,L,this.state.variables,this.state.context,this)),await((R=(P=this.options).onSettled)==null?void 0:R.call(P,void 0,L,t,this.state.context)),L}finally{Pe(this,Nt,ln).call(this,{type:"error",error:L})}}finally{b(this,_e).runNext(this)}}},Tt=new WeakMap,_e=new WeakMap,Xn=new WeakMap,Nt=new WeakSet,ln=function(t){const n=r=>{switch(t.type){case"failed":return{...r,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...r,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:t.error,failureCount:r.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=n(this.state),Le.batch(()=>{b(this,Tt).forEach(r=>{r.onMutationUpdate(t)}),b(this,_e).notify({mutation:this,type:"updated",action:t})})},ff);function tS(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var $t,mt,ei,pf,nS=(pf=class extends Zs{constructor(t={}){super();Z(this,$t);Z(this,mt);Z(this,ei);this.config=t,B(this,$t,new Set),B(this,mt,new Map),B(this,ei,0)}build(t,n,r){const o=new eS({mutationCache:this,mutationId:++fi(this,ei)._,options:t.defaultMutationOptions(n),state:r});return this.add(o),o}add(t){b(this,$t).add(t);const n=ji(t);if(typeof n=="string"){const r=b(this,mt).get(n);r?r.push(t):b(this,mt).set(n,[t])}this.notify({type:"added",mutation:t})}remove(t){if(b(this,$t).delete(t)){const n=ji(t);if(typeof n=="string"){const r=b(this,mt).get(n);if(r)if(r.length>1){const o=r.indexOf(t);o!==-1&&r.splice(o,1)}else r[0]===t&&b(this,mt).delete(n)}}this.notify({type:"removed",mutation:t})}canRun(t){const n=ji(t);if(typeof n=="string"){const r=b(this,mt).get(n),o=r==null?void 0:r.find(i=>i.state.status==="pending");return!o||o===t}else return!0}runNext(t){var r;const n=ji(t);if(typeof n=="string"){const o=(r=b(this,mt).get(n))==null?void 0:r.find(i=>i!==t&&i.state.isPaused);return(o==null?void 0:o.continue())??Promise.resolve()}else return Promise.resolve()}clear(){Le.batch(()=>{b(this,$t).forEach(t=>{this.notify({type:"removed",mutation:t})}),b(this,$t).clear(),b(this,mt).clear()})}getAll(){return Array.from(b(this,$t))}find(t){const n={exact:!0,...t};return this.getAll().find(r=>Kd(n,r))}findAll(t={}){return this.getAll().filter(n=>Kd(t,n))}notify(t){Le.batch(()=>{this.listeners.forEach(n=>{n(t)})})}resumePausedMutations(){const t=this.getAll().filter(n=>n.state.isPaused);return Le.batch(()=>Promise.all(t.map(n=>n.continue().catch(pt))))}},$t=new WeakMap,mt=new WeakMap,ei=new WeakMap,pf);function ji(e){var t;return(t=e.options.scope)==null?void 0:t.id}function Xd(e){return{onFetch:(t,n)=>{var c,f,h,d,w;const r=t.options,o=(h=(f=(c=t.fetchOptions)==null?void 0:c.meta)==null?void 0:f.fetchMore)==null?void 0:h.direction,i=((d=t.state.data)==null?void 0:d.pages)||[],s=((w=t.state.data)==null?void 0:w.pageParams)||[];let l={pages:[],pageParams:[]},a=0;const u=async()=>{let v=!1;const y=g=>{Object.defineProperty(g,"signal",{enumerable:!0,get:()=>(t.signal.aborted?v=!0:t.signal.addEventListener("abort",()=>{v=!0}),t.signal)})},m=Qm(t.options,t.fetchOptions),p=async(g,S,C)=>{if(v)return Promise.reject();if(S==null&&g.pages.length)return Promise.resolve(g);const P=(()=>{const F={client:t.client,queryKey:t.queryKey,pageParam:S,direction:C?"backward":"forward",meta:t.options.meta};return y(F),F})(),R=await m(P),{maxPages:L}=t.options,A=C?V1:B1;return{pages:A(g.pages,R,L),pageParams:A(g.pageParams,S,L)}};if(o&&i.length){const g=o==="backward",S=g?rS:qd,C={pages:i,pageParams:s},k=S(r,C);l=await p(C,k,g)}else{const g=e??i.length;do{const S=a===0?s[0]??r.initialPageParam:qd(r,l);if(a>0&&S==null)break;l=await p(l,S),a++}while(a<g)}return l};t.options.persister?t.fetchFn=()=>{var v,y;return(y=(v=t.options).persister)==null?void 0:y.call(v,u,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n)}:t.fetchFn=u}}}function qd(e,{pages:t,pageParams:n}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,n[r],n):void 0}function rS(e,{pages:t,pageParams:n}){var r;return t.length>0?(r=e.getPreviousPageParam)==null?void 0:r.call(e,t[0],t,n[0],n):void 0}var de,hn,mn,$r,Ur,gn,Br,Vr,hf,oS=(hf=class{constructor(e={}){Z(this,de);Z(this,hn);Z(this,mn);Z(this,$r);Z(this,Ur);Z(this,gn);Z(this,Br);Z(this,Vr);B(this,de,e.queryCache||new J1),B(this,hn,e.mutationCache||new nS),B(this,mn,e.defaultOptions||{}),B(this,$r,new Map),B(this,Ur,new Map),B(this,gn,0)}mount(){fi(this,gn)._++,b(this,gn)===1&&(B(this,Br,Km.subscribe(async e=>{e&&(await this.resumePausedMutations(),b(this,de).onFocus())})),B(this,Vr,ks.subscribe(async e=>{e&&(await this.resumePausedMutations(),b(this,de).onOnline())})))}unmount(){var e,t;fi(this,gn)._--,b(this,gn)===0&&((e=b(this,Br))==null||e.call(this),B(this,Br,void 0),(t=b(this,Vr))==null||t.call(this),B(this,Vr,void 0))}isFetching(e){return b(this,de).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return b(this,hn).findAll({...e,status:"pending"}).length}getQueryData(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=b(this,de).get(t.queryHash))==null?void 0:n.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),n=b(this,de).build(this,t),r=n.state.data;return r===void 0?this.fetchQuery(e):(e.revalidateIfStale&&n.isStaleByTime(Qa(t.staleTime,n))&&this.prefetchQuery(t),Promise.resolve(r))}getQueriesData(e){return b(this,de).findAll(e).map(({queryKey:t,state:n})=>{const r=n.data;return[t,r]})}setQueryData(e,t,n){const r=this.defaultQueryOptions({queryKey:e}),o=b(this,de).get(r.queryHash),i=o==null?void 0:o.state.data,s=D1(t,i);if(s!==void 0)return b(this,de).build(this,r).setData(s,{...n,manual:!0})}setQueriesData(e,t,n){return Le.batch(()=>b(this,de).findAll(e).map(({queryKey:r})=>[r,this.setQueryData(r,t,n)]))}getQueryState(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=b(this,de).get(t.queryHash))==null?void 0:n.state}removeQueries(e){const t=b(this,de);Le.batch(()=>{t.findAll(e).forEach(n=>{t.remove(n)})})}resetQueries(e,t){const n=b(this,de);return Le.batch(()=>(n.findAll(e).forEach(r=>{r.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){const n={revert:!0,...t},r=Le.batch(()=>b(this,de).findAll(e).map(o=>o.cancel(n)));return Promise.all(r).then(pt).catch(pt)}invalidateQueries(e,t={}){return Le.batch(()=>(b(this,de).findAll(e).forEach(n=>{n.invalidate()}),(e==null?void 0:e.refetchType)==="none"?Promise.resolve():this.refetchQueries({...e,type:(e==null?void 0:e.refetchType)??(e==null?void 0:e.type)??"active"},t)))}refetchQueries(e,t={}){const n={...t,cancelRefetch:t.cancelRefetch??!0},r=Le.batch(()=>b(this,de).findAll(e).filter(o=>!o.isDisabled()&&!o.isStatic()).map(o=>{let i=o.fetch(void 0,n);return n.throwOnError||(i=i.catch(pt)),o.state.fetchStatus==="paused"?Promise.resolve():i}));return Promise.all(r).then(pt)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const n=b(this,de).build(this,t);return n.isStaleByTime(Qa(t.staleTime,n))?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(pt).catch(pt)}fetchInfiniteQuery(e){return e.behavior=Xd(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(pt).catch(pt)}ensureInfiniteQueryData(e){return e.behavior=Xd(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return ks.isOnline()?b(this,hn).resumePausedMutations():Promise.resolve()}getQueryCache(){return b(this,de)}getMutationCache(){return b(this,hn)}getDefaultOptions(){return b(this,mn)}setDefaultOptions(e){B(this,mn,e)}setQueryDefaults(e,t){b(this,$r).set(qo(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...b(this,$r).values()],n={};return t.forEach(r=>{Zo(e,r.queryKey)&&Object.assign(n,r.defaultOptions)}),n}setMutationDefaults(e,t){b(this,Ur).set(qo(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...b(this,Ur).values()],n={};return t.forEach(r=>{Zo(e,r.mutationKey)&&Object.assign(n,r.defaultOptions)}),n}defaultQueryOptions(e){if(e._defaulted)return e;const t={...b(this,mn).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=sc(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===lc&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...b(this,mn).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){b(this,de).clear(),b(this,hn).clear()}},de=new WeakMap,hn=new WeakMap,mn=new WeakMap,$r=new WeakMap,Ur=new WeakMap,gn=new WeakMap,Br=new WeakMap,Vr=new WeakMap,hf),iS=x.createContext(void 0),sS=({client:e,children:t})=>(x.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),E.jsx(iS.Provider,{value:e,children:t}));/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Ps(){return Ps=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ps.apply(this,arguments)}var wn;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(wn||(wn={}));const Zd="popstate";function lS(e){e===void 0&&(e={});function t(r,o){let{pathname:i,search:s,hash:l}=r.location;return Ya("",{pathname:i,search:s,hash:l},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function n(r,o){return typeof o=="string"?o:Jm(o)}return uS(t,n,null,e)}function Ve(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Zm(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function aS(){return Math.random().toString(36).substr(2,8)}function Jd(e,t){return{usr:e.state,key:e.key,idx:t}}function Ya(e,t,n,r){return n===void 0&&(n=null),Ps({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?el(t):t,{state:n,key:t&&t.key||r||aS()})}function Jm(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function el(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function uS(e,t,n,r){r===void 0&&(r={});let{window:o=document.defaultView,v5Compat:i=!1}=r,s=o.history,l=wn.Pop,a=null,u=c();u==null&&(u=0,s.replaceState(Ps({},s.state,{idx:u}),""));function c(){return(s.state||{idx:null}).idx}function f(){l=wn.Pop;let y=c(),m=y==null?null:y-u;u=y,a&&a({action:l,location:v.location,delta:m})}function h(y,m){l=wn.Push;let p=Ya(v.location,y,m);u=c()+1;let g=Jd(p,u),S=v.createHref(p);try{s.pushState(g,"",S)}catch(C){if(C instanceof DOMException&&C.name==="DataCloneError")throw C;o.location.assign(S)}i&&a&&a({action:l,location:v.location,delta:1})}function d(y,m){l=wn.Replace;let p=Ya(v.location,y,m);u=c();let g=Jd(p,u),S=v.createHref(p);s.replaceState(g,"",S),i&&a&&a({action:l,location:v.location,delta:0})}function w(y){let m=o.location.origin!=="null"?o.location.origin:o.location.href,p=typeof y=="string"?y:Jm(y);return p=p.replace(/ $/,"%20"),Ve(m,"No window.location.(origin|href) available to create URL for href: "+p),new URL(p,m)}let v={get action(){return l},get location(){return e(o,s)},listen(y){if(a)throw new Error("A history only accepts one active listener");return o.addEventListener(Zd,f),a=y,()=>{o.removeEventListener(Zd,f),a=null}},createHref(y){return t(o,y)},createURL:w,encodeLocation(y){let m=w(y);return{pathname:m.pathname,search:m.search,hash:m.hash}},push:h,replace:d,go(y){return s.go(y)}};return v}var ef;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(ef||(ef={}));function cS(e,t,n){return n===void 0&&(n="/"),dS(e,t,n)}function dS(e,t,n,r){let o=typeof t=="string"?el(t):t,i=ng(o.pathname||"/",n);if(i==null)return null;let s=eg(e);fS(s);let l=null;for(let a=0;l==null&&a<s.length;++a){let u=kS(i);l=SS(s[a],u)}return l}function eg(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let o=(i,s,l)=>{let a={relativePath:l===void 0?i.path||"":l,caseSensitive:i.caseSensitive===!0,childrenIndex:s,route:i};a.relativePath.startsWith("/")&&(Ve(a.relativePath.startsWith(r),'Absolute route path "'+a.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),a.relativePath=a.relativePath.slice(r.length));let u=Mr([r,a.relativePath]),c=n.concat(a);i.children&&i.children.length>0&&(Ve(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),eg(i.children,t,c,u)),!(i.path==null&&!i.index)&&t.push({path:u,score:wS(u,i.index),routesMeta:c})};return e.forEach((i,s)=>{var l;if(i.path===""||!((l=i.path)!=null&&l.includes("?")))o(i,s);else for(let a of tg(i.path))o(i,s,a)}),t}function tg(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,o=n.endsWith("?"),i=n.replace(/\?$/,"");if(r.length===0)return o?[i,""]:[i];let s=tg(r.join("/")),l=[];return l.push(...s.map(a=>a===""?i:[i,a].join("/"))),o&&l.push(...s),l.map(a=>e.startsWith("/")&&a===""?"/":a)}function fS(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:xS(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const pS=/^:[\w-]+$/,hS=3,mS=2,gS=1,vS=10,yS=-2,tf=e=>e==="*";function wS(e,t){let n=e.split("/"),r=n.length;return n.some(tf)&&(r+=yS),t&&(r+=mS),n.filter(o=>!tf(o)).reduce((o,i)=>o+(pS.test(i)?hS:i===""?gS:vS),r)}function xS(e,t){return e.length===t.length&&e.slice(0,-1).every((r,o)=>r===t[o])?e[e.length-1]-t[t.length-1]:0}function SS(e,t,n){let{routesMeta:r}=e,o={},i="/",s=[];for(let l=0;l<r.length;++l){let a=r[l],u=l===r.length-1,c=i==="/"?t:t.slice(i.length)||"/",f=ES({path:a.relativePath,caseSensitive:a.caseSensitive,end:u},c),h=a.route;if(!f)return null;Object.assign(o,f.params),s.push({params:o,pathname:Mr([i,f.pathname]),pathnameBase:PS(Mr([i,f.pathnameBase])),route:h}),f.pathnameBase!=="/"&&(i=Mr([i,f.pathnameBase]))}return s}function ES(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=CS(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let i=o[0],s=i.replace(/(.)\/+$/,"$1"),l=o.slice(1);return{params:r.reduce((u,c,f)=>{let{paramName:h,isOptional:d}=c;if(h==="*"){let v=l[f]||"";s=i.slice(0,i.length-v.length).replace(/(.)\/+$/,"$1")}const w=l[f];return d&&!w?u[h]=void 0:u[h]=(w||"").replace(/%2F/g,"/"),u},{}),pathname:i,pathnameBase:s,pattern:e}}function CS(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Zm(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(s,l,a)=>(r.push({paramName:l,isOptional:a!=null}),a?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),o+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":e!==""&&e!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),r]}function kS(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Zm(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function ng(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}const Mr=e=>e.join("/").replace(/\/\/+/g,"/"),PS=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/");function bS(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const rg=["post","put","patch","delete"];new Set(rg);const TS=["get",...rg];new Set(TS);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function bs(){return bs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},bs.apply(this,arguments)}const NS=x.createContext(null),RS=x.createContext(null),og=x.createContext(null),tl=x.createContext(null),nl=x.createContext({outlet:null,matches:[],isDataRoute:!1}),ig=x.createContext(null);function ac(){return x.useContext(tl)!=null}function sg(){return ac()||Ve(!1),x.useContext(tl).location}function OS(e,t){return _S(e,t)}function _S(e,t,n,r){ac()||Ve(!1);let{navigator:o}=x.useContext(og),{matches:i}=x.useContext(nl),s=i[i.length-1],l=s?s.params:{};s&&s.pathname;let a=s?s.pathnameBase:"/";s&&s.route;let u=sg(),c;if(t){var f;let y=typeof t=="string"?el(t):t;a==="/"||(f=y.pathname)!=null&&f.startsWith(a)||Ve(!1),c=y}else c=u;let h=c.pathname||"/",d=h;if(a!=="/"){let y=a.replace(/^\//,"").split("/");d="/"+h.replace(/^\//,"").split("/").slice(y.length).join("/")}let w=cS(e,{pathname:d}),v=DS(w&&w.map(y=>Object.assign({},y,{params:Object.assign({},l,y.params),pathname:Mr([a,o.encodeLocation?o.encodeLocation(y.pathname).pathname:y.pathname]),pathnameBase:y.pathnameBase==="/"?a:Mr([a,o.encodeLocation?o.encodeLocation(y.pathnameBase).pathname:y.pathnameBase])})),i,n,r);return t&&v?x.createElement(tl.Provider,{value:{location:bs({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:wn.Pop}},v):v}function AS(){let e=$S(),t=bS(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return x.createElement(x.Fragment,null,x.createElement("h2",null,"Unexpected Application Error!"),x.createElement("h3",{style:{fontStyle:"italic"}},t),n?x.createElement("pre",{style:o},n):null,null)}const LS=x.createElement(AS,null);class jS extends x.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?x.createElement(nl.Provider,{value:this.props.routeContext},x.createElement(ig.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function MS(e){let{routeContext:t,match:n,children:r}=e,o=x.useContext(NS);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),x.createElement(nl.Provider,{value:t},r)}function DS(e,t,n,r){var o;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var i;if(!n)return null;if(n.errors)e=n.matches;else if((i=r)!=null&&i.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let s=e,l=(o=n)==null?void 0:o.errors;if(l!=null){let c=s.findIndex(f=>f.route.id&&(l==null?void 0:l[f.route.id])!==void 0);c>=0||Ve(!1),s=s.slice(0,Math.min(s.length,c+1))}let a=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let c=0;c<s.length;c++){let f=s[c];if((f.route.HydrateFallback||f.route.hydrateFallbackElement)&&(u=c),f.route.id){let{loaderData:h,errors:d}=n,w=f.route.loader&&h[f.route.id]===void 0&&(!d||d[f.route.id]===void 0);if(f.route.lazy||w){a=!0,u>=0?s=s.slice(0,u+1):s=[s[0]];break}}}return s.reduceRight((c,f,h)=>{let d,w=!1,v=null,y=null;n&&(d=l&&f.route.id?l[f.route.id]:void 0,v=f.route.errorElement||LS,a&&(u<0&&h===0?(US("route-fallback"),w=!0,y=null):u===h&&(w=!0,y=f.route.hydrateFallbackElement||null)));let m=t.concat(s.slice(0,h+1)),p=()=>{let g;return d?g=v:w?g=y:f.route.Component?g=x.createElement(f.route.Component,null):f.route.element?g=f.route.element:g=c,x.createElement(MS,{match:f,routeContext:{outlet:c,matches:m,isDataRoute:n!=null},children:g})};return n&&(f.route.ErrorBoundary||f.route.errorElement||h===0)?x.createElement(jS,{location:n.location,revalidation:n.revalidation,component:v,error:d,children:p(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):p()},null)}var lg=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(lg||{});function IS(e){let t=x.useContext(RS);return t||Ve(!1),t}function zS(e){let t=x.useContext(nl);return t||Ve(!1),t}function FS(e){let t=zS(),n=t.matches[t.matches.length-1];return n.route.id||Ve(!1),n.route.id}function $S(){var e;let t=x.useContext(ig),n=IS(lg.UseRouteError),r=FS();return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}const nf={};function US(e,t,n){nf[e]||(nf[e]=!0)}function BS(e,t){e==null||e.v7_startTransition,e==null||e.v7_relativeSplatPath}function Ga(e){Ve(!1)}function VS(e){let{basename:t="/",children:n=null,location:r,navigationType:o=wn.Pop,navigator:i,static:s=!1,future:l}=e;ac()&&Ve(!1);let a=t.replace(/^\/*/,"/"),u=x.useMemo(()=>({basename:a,navigator:i,static:s,future:bs({v7_relativeSplatPath:!1},l)}),[a,l,i,s]);typeof r=="string"&&(r=el(r));let{pathname:c="/",search:f="",hash:h="",state:d=null,key:w="default"}=r,v=x.useMemo(()=>{let y=ng(c,a);return y==null?null:{location:{pathname:y,search:f,hash:h,state:d,key:w},navigationType:o}},[a,c,f,h,d,w,o]);return v==null?null:x.createElement(og.Provider,{value:u},x.createElement(tl.Provider,{children:n,value:v}))}function WS(e){let{children:t,location:n}=e;return OS(Xa(t),n)}new Promise(()=>{});function Xa(e,t){t===void 0&&(t=[]);let n=[];return x.Children.forEach(e,(r,o)=>{if(!x.isValidElement(r))return;let i=[...t,o];if(r.type===x.Fragment){n.push.apply(n,Xa(r.props.children,i));return}r.type!==Ga&&Ve(!1),!r.props.index||!r.props.children||Ve(!1);let s={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(s.children=Xa(r.props.children,i)),n.push(s)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const HS="6";try{window.__reactRouterVersion=HS}catch{}const QS="startTransition",rf=bf[QS];function KS(e){let{basename:t,children:n,future:r,window:o}=e,i=x.useRef();i.current==null&&(i.current=lS({window:o,v5Compat:!0}));let s=i.current,[l,a]=x.useState({action:s.action,location:s.location}),{v7_startTransition:u}=r||{},c=x.useCallback(f=>{u&&rf?rf(()=>a(f)):a(f)},[a,u]);return x.useLayoutEffect(()=>s.listen(c),[s,c]),x.useEffect(()=>BS(r),[r]),x.createElement(VS,{basename:t,children:n,location:l.location,navigationType:l.action,navigator:s,future:r})}var of;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(of||(of={}));var sf;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(sf||(sf={}));const YS=({scrollY:e})=>E.jsxs("section",{id:"home",className:"min-h-screen flex items-center justify-center px-4 relative",children:[E.jsxs("div",{className:"text-center max-w-5xl mx-auto animate-fade-in",style:{transform:`translateY(${e*.2}px)`},children:[E.jsxs("h1",{className:"text-7xl md:text-8xl font-light text-slate-800 mb-12 leading-tight",children:[E.jsx("span",{className:"gradient-text-animated pulse-glow",children:"Reinigung"})," neu definiert"]}),E.jsx("p",{className:"text-2xl md:text-3xl text-slate-600 mb-6 font-light leading-relaxed",children:"mit Stil, Präzision und Vertrauen"}),E.jsx("p",{className:"text-xl text-slate-500 mb-16 max-w-3xl mx-auto leading-relaxed",children:"Zuverlässige Reinigung für Hotels, Büros und Wohnanlagen – von Profis für Profis."}),E.jsxs("div",{className:"flex flex-col sm:flex-row gap-6 justify-center",children:[E.jsx("a",{href:"https://wa.me/4917623152477",target:"_blank",rel:"noopener noreferrer",className:"premium-button bg-gradient-to-r from-green-500 to-green-600 text-white px-10 py-5 rounded-full font-semibold hover:scale-105 transition-all duration-300 shadow-2xl text-lg",children:"WhatsApp Kontakt"}),E.jsx("a",{href:"mailto:<EMAIL>",className:"premium-button bg-gradient-to-r from-blue-500 to-blue-600 text-white px-10 py-5 rounded-full font-semibold hover:scale-105 transition-all duration-300 shadow-2xl text-lg",children:"E-Mail senden"})]})]}),E.jsxs("div",{className:"absolute inset-0 overflow-hidden pointer-events-none",children:[E.jsx("div",{className:"floating-element absolute top-20 left-10 w-40 h-40 bg-blue-300/20 rounded-full blur-3xl"}),E.jsx("div",{className:"floating-element absolute top-40 right-20 w-32 h-32 bg-cyan-300/20 rounded-full blur-2xl",style:{animationDelay:"2s"}}),E.jsx("div",{className:"floating-element absolute bottom-20 left-1/4 w-48 h-48 bg-sky-300/20 rounded-full blur-3xl",style:{animationDelay:"4s"}})]})]}),GS=()=>{const e=[{title:"Hotelzimmerreinigung",description:"Tiefenreinigung und tägliche Pflege für höchste Hygienestandards in Hotelzimmern.",icon:"🏨"},{title:"Teppichreinigung",description:"Tiefenreinigung für Teppiche und Polster. Wir entfernen Flecken, Gerüche und Allergene für ein frisches und hygienisches Raumklima.",icon:"🏠"},{title:"Bodenreinigung",description:"Professionelle Pflege für Hartböden, Fliesen, Laminat und mehr. Wir sorgen für glänzende, hygienisch saubere Oberflächen.",icon:"✨"},{title:"Gemeinschaftsräume",description:"Zuverlässige Reinigung von Treppenhäusern, Fluren und Gemeinschaftsbereichen für Mehrfamilienhäuser und Wohnanlagen.",icon:"🏢"},{title:"Büroreinigung",description:"Professionelle Reinigung von Büroflächen und Arbeitsplätzen für ein sauberes und produktives Arbeitsumfeld.",icon:"💼"},{title:"Desinfektion",description:"Gründliche Desinfektion von Räumen und Oberflächen zur Bekämpfung von Keimen, Bakterien und Viren für maximale Hygiene und Sicherheit.",icon:"🧽"}];return E.jsx("section",{id:"services",className:"py-24 px-4",children:E.jsxs("div",{className:"max-w-7xl mx-auto",children:[E.jsxs("div",{className:"text-center mb-20 animate-fade-in",children:[E.jsxs("h2",{className:"text-6xl font-light text-slate-800 mb-8",children:["Unsere ",E.jsx("span",{className:"gradient-text",children:"Leistungen"})]}),E.jsx("p",{className:"text-2xl text-slate-600 max-w-3xl mx-auto",children:"Professionelle Reinigungslösungen für jeden Bedarf"})]}),E.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-10",children:e.map((t,n)=>E.jsxs("div",{className:"service-card-premium glass-morphism-premium p-10 rounded-3xl border border-white/30 hover:scale-105 transition-all duration-500 animate-fade-in group shadow-2xl",style:{animationDelay:`${n*.15}s`},children:[E.jsx("div",{className:"icon-badge mb-6",children:E.jsx("div",{className:"text-5xl group-hover:scale-110 transition-transform duration-300",children:t.icon})}),E.jsx("h3",{className:"text-2xl font-semibold text-slate-800 mb-6",children:t.title}),E.jsx("p",{className:"text-slate-600 leading-relaxed text-lg",children:t.description})]},n))})]})})},XS=()=>{const e=[{name:"Sarah Ahmed",role:"Teamleitung",image:"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=face"},{name:"Maria Schmidt",role:"Fachkraft",image:"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=300&h=300&fit=crop&crop=face"},{name:"Fatima Hassan",role:"Fachkraft",image:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face"},{name:"Anna Weber",role:"Qualitätskontrolle",image:"https://images.unsplash.com/photo-1531123897727-8f129e1688ce?w=300&h=300&fit=crop&crop=face"}];return E.jsx("section",{id:"team",className:"py-24 px-4 bg-gradient-to-b from-transparent to-blue-50/30",children:E.jsxs("div",{className:"max-w-6xl mx-auto",children:[E.jsxs("div",{className:"text-center mb-20 animate-fade-in",children:[E.jsxs("h2",{className:"text-6xl font-light text-slate-800 mb-8",children:["Unser ",E.jsx("span",{className:"gradient-text",children:"Team"})]}),E.jsx("p",{className:"text-2xl text-slate-600 max-w-3xl mx-auto",children:"Professionelle Reinigungskräfte mit Leidenschaft für Sauberkeit"})]}),E.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:e.map((t,n)=>E.jsxs("div",{className:"team-card glass-morphism-premium p-8 rounded-3xl border border-white/30 text-center hover:scale-105 transition-all duration-500 animate-fade-in shadow-2xl",style:{animationDelay:`${n*.1}s`},children:[E.jsx("div",{className:"mb-6",children:E.jsx("img",{src:t.image,alt:t.name,className:"w-24 h-24 rounded-full mx-auto object-cover border-4 border-white/50 shadow-lg"})}),E.jsx("h3",{className:"text-xl font-semibold text-slate-800 mb-2",children:t.name}),E.jsx("p",{className:"text-blue-600 font-medium",children:t.role})]},n))})]})})},qS=()=>E.jsx("section",{id:"contact",className:"py-24 px-4",children:E.jsx("div",{className:"max-w-5xl mx-auto text-center",children:E.jsxs("div",{className:"glass-morphism-premium p-16 rounded-3xl border border-white/30 animate-fade-in shadow-2xl",children:[E.jsxs("h2",{className:"text-6xl font-light text-slate-800 mb-8",children:[E.jsx("span",{className:"gradient-text",children:"Kontakt"})," aufnehmen"]}),E.jsx("p",{className:"text-2xl text-slate-600 mb-12",children:"Kontaktieren Sie uns jetzt – schnell & unkompliziert!"}),E.jsxs("div",{className:"flex flex-col sm:flex-row justify-center gap-8 mb-16",children:[E.jsxs("a",{href:"https://wa.me/4917623152477",target:"_blank",rel:"noopener noreferrer",className:"premium-button-3d bg-gradient-to-r from-green-500 to-green-600 text-white px-12 py-6 rounded-full text-xl font-semibold hover:scale-105 transition-all duration-300 shadow-2xl flex items-center justify-center gap-3",children:[E.jsx(Fa,{className:"w-6 h-6"}),"WhatsApp"]}),E.jsxs("a",{href:"mailto:<EMAIL>",className:"premium-button-3d bg-gradient-to-r from-blue-500 to-blue-600 text-white px-12 py-6 rounded-full text-xl font-semibold hover:scale-105 transition-all duration-300 shadow-2xl flex items-center justify-center gap-3",children:[E.jsx(za,{className:"w-6 h-6"}),"E-Mail"]})]}),E.jsxs("div",{className:"grid md:grid-cols-3 gap-8 text-slate-600",children:[E.jsxs("div",{className:"flex flex-col items-center",children:[E.jsx(Fa,{className:"w-8 h-8 text-blue-500 mb-4"}),E.jsx("h3",{className:"font-semibold text-slate-800 mb-3 text-lg",children:"Telefon"}),E.jsx("p",{className:"text-lg",children:"+49 176 23152477"})]}),E.jsxs("div",{className:"flex flex-col items-center",children:[E.jsx(za,{className:"w-8 h-8 text-blue-500 mb-4"}),E.jsx("h3",{className:"font-semibold text-slate-800 mb-3 text-lg",children:"E-Mail"}),E.jsx("p",{className:"text-lg",children:"<EMAIL>"})]}),E.jsxs("div",{className:"flex flex-col items-center",children:[E.jsx(H0,{className:"w-8 h-8 text-blue-500 mb-4"}),E.jsx("h3",{className:"font-semibold text-slate-800 mb-3 text-lg",children:"Adresse"}),E.jsxs("p",{className:"text-lg",children:["Paul-Langen-Straße 39",E.jsx("br",{}),"53229 Bonn"]})]})]})]})})}),ZS=({scrollToSection:e})=>E.jsx("nav",{className:"fixed top-6 left-1/2 transform -translate-x-1/2 z-50 animate-fade-in",children:E.jsx("div",{className:"glass-morphism-premium px-8 py-4 rounded-full border border-white/30 shadow-xl backdrop-blur-xl",children:E.jsxs("div",{className:"flex items-center space-x-10",children:[E.jsx("button",{onClick:()=>e("home"),className:"text-slate-700 hover:text-blue-600 transition-all duration-300 font-medium text-base hover:scale-105",children:"Startseite"}),E.jsx("button",{onClick:()=>e("services"),className:"text-slate-700 hover:text-blue-600 transition-all duration-300 font-medium text-base hover:scale-105",children:"Leistungen"}),E.jsx("button",{onClick:()=>e("team"),className:"text-slate-700 hover:text-blue-600 transition-all duration-300 font-medium text-base hover:scale-105",children:"Unser Team"}),E.jsx("button",{onClick:()=>e("contact"),className:"text-slate-700 hover:text-blue-600 transition-all duration-300 font-medium text-base hover:scale-105",children:"Kontakt"})]})})}),JS=()=>E.jsx("footer",{className:"py-12 px-4 text-center",children:E.jsxs("div",{className:"glass-morphism-premium p-8 rounded-3xl border border-white/30 max-w-3xl mx-auto shadow-xl",children:[E.jsxs("div",{className:"flex justify-center gap-6 mb-6",children:[E.jsx("a",{href:"https://wa.me/4917623152477",target:"_blank",rel:"noopener noreferrer",className:"text-green-600 hover:text-green-700 transition-colors duration-300",children:E.jsx(Fa,{className:"w-8 h-8"})}),E.jsx("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:text-blue-700 transition-colors duration-300",children:E.jsx(za,{className:"w-8 h-8"})})]}),E.jsx("p",{className:"text-slate-600 mb-3 text-lg font-medium",children:"www.suzreinigung.de"}),E.jsx("p",{className:"text-slate-500 text-base",children:"© 2024 SUZ Reinigung. Alle Rechte vorbehalten."})]})}),eE=()=>{const[e,t]=x.useState(0);x.useEffect(()=>{const r=()=>t(window.scrollY);return window.addEventListener("scroll",r),()=>window.removeEventListener("scroll",r)},[]);const n=r=>{const o=document.getElementById(r);o==null||o.scrollIntoView({behavior:"smooth"})};return E.jsxs("div",{className:"min-h-screen bg-premium-gradient overflow-x-hidden",children:[E.jsx(ZS,{scrollToSection:n}),E.jsx("div",{className:"fixed top-8 left-8 z-50 animate-fade-in",children:E.jsx("div",{className:"glass-morphism-premium p-4 rounded-3xl border border-white/30 shadow-xl backdrop-blur-xl logo-glow group",children:E.jsx("img",{src:"/assets/logos/logo.png",alt:"SUZ Reinigung Logo",className:"w-16 h-16 object-contain transition-all duration-300 group-hover:scale-110 min-w-[40px] min-h-[40px]"})})}),E.jsx(YS,{scrollY:e}),E.jsx(GS,{}),E.jsx(XS,{}),E.jsx(qS,{}),E.jsx(JS,{})]})},tE=()=>{const e=sg();return x.useEffect(()=>{console.error("404 Error: User attempted to access non-existent route:",e.pathname)},[e.pathname]),E.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:E.jsxs("div",{className:"text-center",children:[E.jsx("h1",{className:"text-4xl font-bold mb-4",children:"404"}),E.jsx("p",{className:"text-xl text-gray-600 mb-4",children:"Oops! Page not found"}),E.jsx("a",{href:"/",className:"text-blue-500 hover:text-blue-700 underline",children:"Return to Home"})]})})},nE=new oS,rE=()=>E.jsx(sS,{client:nE,children:E.jsxs(j1,{children:[E.jsx(Nw,{}),E.jsx(sx,{}),E.jsx(KS,{children:E.jsxs(WS,{children:[E.jsx(Ga,{path:"/",element:E.jsx(eE,{})}),E.jsx(Ga,{path:"*",element:E.jsx(tE,{})})]})})]})});Oh(document.getElementById("root")).render(E.jsx(rE,{}));
